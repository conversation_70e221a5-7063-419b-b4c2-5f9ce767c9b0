#!/usr/bin/env python3
"""
AutoGen编程工作流快速开始脚本
简化版本，适合快速体验和测试
"""

import asyncio
import os
import sys
from pathlib import Path

# 检查并导入必要的模块
try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_agentchat.ui import Console
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError as e:
    print(f"❌ 导入AutoGen模块失败: {e}")
    print("请先运行: python setup.py")
    sys.exit(1)


class QuickWorkflow:
    """快速工作流类 - 简化版本"""
    
    def __init__(self, api_key: str = None):
        """初始化快速工作流"""
        # 获取API密钥
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量或传入api_key参数")
        
        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o-mini",  # 使用更经济的模型
            api_key=self.api_key,
            temperature=0.7
        )
        
        # 创建工作目录
        Path("quick_results").mkdir(exist_ok=True)
        
        # 设置agents
        self._setup_agents()
        self._setup_team()
    
    def _setup_agents(self):
        """设置简化的agents"""
        
        # 代码编写agent
        self.coder = AssistantAgent(
            name="Coder",
            model_client=self.model_client,
            system_message="""你是代码编写专家。请：
1. 根据需求编写清晰的代码
2. 添加必要的注释
3. 考虑错误处理
4. 完成后说 'CODE_READY'"""
        )
        
        # 代码审查agent
        self.reviewer = AssistantAgent(
            name="Reviewer", 
            model_client=self.model_client,
            system_message="""你是代码审查专家。请：
1. 检查代码质量和正确性
2. 指出潜在问题
3. 提出改进建议
4. 完成后说 'REVIEW_DONE'"""
        )
        
        # 代码优化agent
        self.optimizer = AssistantAgent(
            name="Optimizer",
            model_client=self.model_client,
            system_message="""你是代码优化专家。请：
1. 根据审查建议优化代码
2. 提升性能和可读性
3. 修复发现的问题
4. 完成后说 'OPTIMIZATION_COMPLETE'"""
        )
    
    def _setup_team(self):
        """设置团队"""
        # 简单的终止条件
        termination = (
            TextMentionTermination("OPTIMIZATION_COMPLETE") |
            MaxMessageTermination(max_messages=10)
        )
        
        # 创建团队
        self.team = RoundRobinGroupChat(
            participants=[self.coder, self.reviewer, self.optimizer],
            termination_condition=termination
        )
    
    async def run(self, task: str):
        """运行工作流"""
        print(f"🚀 开始处理任务: {task[:50]}...")
        print("-" * 50)
        
        # 运行团队协作
        result = await Console(self.team.run_stream(task=task))
        
        print("\n✅ 任务完成!")
        return result
    
    async def close(self):
        """关闭连接"""
        await self.model_client.close()


# 预定义的简单任务
QUICK_TASKS = {
    "1": {
        "name": "实现栈数据结构",
        "task": """请实现一个简单的栈(Stack)类，包含：
- push(item): 添加元素
- pop(): 移除并返回顶部元素  
- peek(): 查看顶部元素
- is_empty(): 检查是否为空
- size(): 获取大小
包含基本的错误处理。"""
    },
    
    "2": {
        "name": "实现二分查找",
        "task": """请实现二分查找算法，要求：
- 函数接受有序数组和目标值
- 返回目标值的索引，未找到返回-1
- 包含递归和迭代两种实现
- 添加详细注释说明算法逻辑"""
    },
    
    "3": {
        "name": "实现简单计算器",
        "task": """请实现一个简单的计算器类，支持：
- 基本运算：加减乘除
- 支持小数运算
- 包含除零错误处理
- 提供清除功能
- 简单易用的接口"""
    },
    
    "4": {
        "name": "实现链表",
        "task": """请实现一个单向链表，包含：
- 节点类定义
- 插入、删除、查找操作
- 遍历和打印功能
- 获取长度方法
- 处理边界情况"""
    }
}


async def interactive_mode():
    """交互模式"""
    print("🎯 AutoGen编程工作流 - 快速体验版")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请先设置OPENAI_API_KEY环境变量")
        print("   export OPENAI_API_KEY='your-api-key'")
        return
    
    try:
        # 创建工作流
        workflow = QuickWorkflow()
        
        while True:
            print("\n📋 选择任务:")
            for key, task_info in QUICK_TASKS.items():
                print(f"  {key}. {task_info['name']}")
            print("  c. 自定义任务")
            print("  q. 退出")
            
            choice = input("\n请选择 (1-4/c/q): ").strip().lower()
            
            if choice == 'q':
                break
            elif choice == 'c':
                custom_task = input("\n请输入自定义任务描述: ").strip()
                if custom_task:
                    await workflow.run(custom_task)
            elif choice in QUICK_TASKS:
                task_info = QUICK_TASKS[choice]
                print(f"\n执行任务: {task_info['name']}")
                await workflow.run(task_info['task'])
            else:
                print("❌ 无效选择，请重试")
        
        await workflow.close()
        print("\n👋 感谢使用!")
        
    except Exception as e:
        print(f"❌ 执行错误: {e}")


async def demo_mode():
    """演示模式 - 自动运行示例"""
    print("🎯 AutoGen编程工作流 - 演示模式")
    print("=" * 50)
    
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请先设置OPENAI_API_KEY环境变量")
        return
    
    try:
        workflow = QuickWorkflow()
        
        # 运行第一个示例任务
        demo_task = QUICK_TASKS["1"]
        print(f"🚀 演示任务: {demo_task['name']}")
        
        await workflow.run(demo_task['task'])
        
        await workflow.close()
        print("\n🎉 演示完成!")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")


def show_help():
    """显示帮助信息"""
    help_text = """
🎯 AutoGen编程工作流 - 快速开始

使用方法:
  python quick_start.py              # 交互模式
  python quick_start.py --demo       # 演示模式
  python quick_start.py --help       # 显示帮助

环境准备:
  1. 安装依赖: pip install autogen-agentchat[openai] autogen-ext[openai]
  2. 设置API密钥: export OPENAI_API_KEY="your-key"

特性:
  ✅ 三Agent协作 (编写→审查→优化)
  ✅ 简化配置，快速上手
  ✅ 预定义常见编程任务
  ✅ 支持自定义任务
  ✅ 实时对话显示

注意:
  - 使用gpt-4o-mini模型以降低成本
  - 限制最大消息数为10条
  - 结果保存在quick_results/目录
"""
    print(help_text)


async def main():
    """主函数"""
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h', 'help']:
            show_help()
        elif arg == '--demo':
            await demo_mode()
        else:
            print(f"未知参数: {arg}")
            show_help()
    else:
        await interactive_mode()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
