#!/usr/bin/env python3
"""
AutoGen编程工作流运行示例
演示如何使用基础和高级工作流
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from autogen_programming_workflow import ProgrammingWorkflow
from advanced_autogen_workflow import AdvancedProgrammingWorkflow
from config import WorkflowConfig, get_task_from_template


async def run_basic_example():
    """运行基础工作流示例"""
    print("🔥 基础工作流示例")
    print("=" * 60)
    
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务：实现栈数据结构
        task = """
        请实现一个Python的栈(Stack)数据结构，要求：
        
        1. 基本操作：
           - push(item): 压入元素
           - pop(): 弹出顶部元素
           - peek(): 查看顶部元素（不弹出）
           - is_empty(): 检查是否为空
           - size(): 获取栈大小
        
        2. 质量要求：
           - 完整的错误处理（如空栈弹出）
           - 详细的文档字符串
           - 类型提示
           - 使用示例
        
        3. 额外特性：
           - 支持迭代器协议
           - 支持字符串表示
           - 线程安全考虑
        """
        
        print("📋 任务: 实现栈数据结构")
        result = await workflow.run_programming_task(task)
        
        print(f"\n✅ 基础工作流完成!")
        print(f"状态: {result['status']}")
        print(f"参与者: {', '.join(result['participants'])}")
        
    except Exception as e:
        print(f"❌ 基础工作流执行失败: {e}")
    
    finally:
        await workflow.close()


async def run_advanced_example():
    """运行高级工作流示例"""
    print("\n🚀 高级工作流示例")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 使用模板生成复杂任务
        task = get_task_from_template(
            "system_design",
            system_name="分布式任务调度器"
        ) + """
        
        具体技术要求：
        1. 核心组件：
           - 任务队列管理器
           - 工作节点管理器  
           - 负载均衡器
           - 故障检测和恢复机制
        
        2. 功能特性：
           - 支持任务优先级
           - 支持任务依赖关系
           - 支持定时任务
           - 支持任务重试机制
           - 提供监控和统计接口
        
        3. 性能要求：
           - 高并发处理能力
           - 低延迟任务分发
           - 水平扩展支持
           - 内存和CPU优化
        
        4. 可靠性：
           - 数据持久化
           - 集群容错
           - 优雅关闭
           - 日志和监控
        """
        
        print("📋 任务: 设计分布式任务调度器")
        result = await workflow.run_advanced_task(task)
        
        print(f"\n✅ 高级工作流完成!")
        print(f"状态: {result['status']}")
        print(f"工作流类型: {result['workflow_type']}")
        print(f"参与者: {', '.join(result['participants'])}")
        
    except Exception as e:
        print(f"❌ 高级工作流执行失败: {e}")
    
    finally:
        await workflow.close()


async def run_custom_task():
    """运行自定义任务示例"""
    print("\n🎯 自定义任务示例")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 自定义算法实现任务
        task = """
        请实现一个高效的布隆过滤器(Bloom Filter)，要求：
        
        1. 核心功能：
           - add(item): 添加元素
           - contains(item): 检查元素是否可能存在
           - 支持自定义哈希函数数量
           - 支持自定义位数组大小
        
        2. 算法优化：
           - 使用多个独立的哈希函数
           - 优化内存使用（位操作）
           - 计算最优参数（基于预期元素数量和误报率）
        
        3. 高级特性：
           - 支持序列化和反序列化
           - 提供统计信息（当前元素数量估计、误报率估计）
           - 支持位数组的动态扩展
           - 线程安全实现
        
        4. 性能分析：
           - 时间复杂度分析
           - 空间复杂度分析
           - 误报率理论计算
           - 性能基准测试
        
        5. 实际应用：
           - 提供使用示例
           - 与Python set的性能对比
           - 适用场景说明
        """
        
        print("📋 任务: 实现布隆过滤器")
        result = await workflow.run_advanced_task(task)
        
        print(f"\n✅ 自定义任务完成!")
        print(f"状态: {result['status']}")
        
    except Exception as e:
        print(f"❌ 自定义任务执行失败: {e}")
    
    finally:
        await workflow.close()


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查配置
    issues = WorkflowConfig.validate_config()
    if issues:
        print("❌ 配置问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    # 创建工作目录
    WorkflowConfig.setup_directories()
    
    print("✅ 环境检查通过")
    return True


async def main():
    """主函数"""
    print("🎯 AutoGen编程工作流演示")
    print("=" * 80)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请检查配置后重试")
        return
    
    try:
        # 运行示例
        print("\n🚀 开始运行示例...")
        
        # 1. 基础工作流示例
        await run_basic_example()
        
        # 2. 高级工作流示例  
        await run_advanced_example()
        
        # 3. 自定义任务示例
        await run_custom_task()
        
        print("\n🎉 所有示例运行完成!")
        print("\n📁 请查看 coding_workspace/ 目录中的生成文件")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def show_usage():
    """显示使用说明"""
    print("""
🎯 AutoGen编程工作流使用说明

环境准备:
1. 安装依赖: pip install autogen-agentchat[openai] autogen-ext[openai]
2. 设置API密钥: export OPENAI_API_KEY="your-key"

运行方式:
1. 运行所有示例: python run_example.py
2. 仅运行基础示例: python run_example.py --basic
3. 仅运行高级示例: python run_example.py --advanced
4. 仅运行自定义示例: python run_example.py --custom

文件说明:
- autogen_programming_workflow.py: 基础工作流
- advanced_autogen_workflow.py: 高级工作流（带工具）
- config.py: 配置文件
- README.md: 详细文档

输出目录:
- coding_workspace/: 生成的代码文件
- results/: 工作流结果
- logs/: 执行日志
""")


if __name__ == "__main__":
    # 解析命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ["-h", "--help", "help"]:
            show_usage()
            sys.exit(0)
        elif arg == "--basic":
            asyncio.run(run_basic_example())
        elif arg == "--advanced":
            asyncio.run(run_advanced_example())
        elif arg == "--custom":
            asyncio.run(run_custom_task())
        else:
            print(f"未知参数: {arg}")
            show_usage()
            sys.exit(1)
    else:
        # 运行所有示例
        asyncio.run(main())
