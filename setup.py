#!/usr/bin/env python3
"""
AutoGen编程工作流安装和设置脚本
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                AutoGen编程工作流安装程序                      ║
║                                                              ║
║  基于Microsoft AutoGen框架的智能编程协作系统                  ║
║  支持代码编写、审查、优化的三Agent协作模式                     ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        # 升级pip
        print("   升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装核心依赖
        print("   安装AutoGen核心包...")
        core_packages = [
            "autogen-agentchat[openai]>=0.4.0",
            "autogen-ext[openai]>=0.4.0", 
            "autogen-core>=0.4.0"
        ]
        
        for package in core_packages:
            print(f"   安装 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                          check=True, capture_output=True)
        
        # 安装其他依赖
        if Path("requirements.txt").exists():
            print("   从requirements.txt安装其他依赖...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                          check=True, capture_output=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖包失败: {e}")
        return False


def setup_environment():
    """设置环境"""
    print("\n🔧 设置环境...")
    
    # 创建工作目录
    directories = [
        "coding_workspace",
        "results", 
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   创建目录: {directory}/")
    
    # 创建环境变量文件模板
    env_file = Path(".env")
    if not env_file.exists():
        env_template = """# AutoGen编程工作流环境变量配置

# OpenAI API配置（必需）
OPENAI_API_KEY=your-openai-api-key-here

# 可选配置
AUTOGEN_MODEL=gpt-4o
AUTOGEN_TEMPERATURE=0.7
AUTOGEN_MAX_MESSAGES=15
AUTOGEN_TIMEOUT=300

# 工作目录配置
WORKSPACE_DIR=coding_workspace
RESULTS_DIR=results
LOGS_DIR=logs
"""
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_template)
        print(f"   创建环境变量模板: {env_file}")
    
    print("✅ 环境设置完成")
    return True


def check_api_key():
    """检查API密钥"""
    print("\n🔑 检查API密钥...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  未设置OPENAI_API_KEY环境变量")
        print("   请设置您的OpenAI API密钥:")
        print("   方式1: export OPENAI_API_KEY='your-key'")
        print("   方式2: 编辑.env文件")
        return False
    
    if api_key == "your-openai-api-key-here":
        print("⚠️  请在.env文件中设置真实的API密钥")
        return False
    
    print("✅ API密钥已设置")
    return True


def run_test():
    """运行测试"""
    print("\n🧪 运行基础测试...")
    
    try:
        # 测试导入
        print("   测试模块导入...")
        import autogen_agentchat
        import autogen_ext
        import autogen_core
        print("   ✅ AutoGen模块导入成功")
        
        # 测试配置
        print("   测试配置文件...")
        from config import WorkflowConfig
        issues = WorkflowConfig.validate_config()
        if issues:
            print("   ⚠️  配置问题:")
            for issue in issues:
                print(f"      - {issue}")
        else:
            print("   ✅ 配置验证通过")
        
        print("✅ 基础测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n🎯 后续步骤:")
    print("1. 设置OpenAI API密钥:")
    print("   export OPENAI_API_KEY='your-api-key'")
    print("   或编辑.env文件")
    print()
    print("2. 运行示例:")
    print("   python run_example.py")
    print()
    print("3. 查看文档:")
    print("   cat README.md")
    print()
    print("4. 自定义使用:")
    print("   from autogen_programming_workflow import ProgrammingWorkflow")
    print()
    print("📁 生成的文件将保存在 coding_workspace/ 目录中")


def main():
    """主函数"""
    print_banner()
    
    success = True
    
    # 检查Python版本
    if not check_python_version():
        success = False
    
    # 安装依赖
    if success and not install_dependencies():
        success = False
    
    # 设置环境
    if success and not setup_environment():
        success = False
    
    # 运行测试
    if success and not run_test():
        success = False
    
    # 检查API密钥（非必需，只是提醒）
    check_api_key()
    
    print("\n" + "="*60)
    if success:
        print("🎉 安装完成!")
        show_next_steps()
    else:
        print("❌ 安装过程中出现问题，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
