const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json()); // for parsing application/json

// Test Route
app.get('/', (req, res) => {
    res.send('AI Education Backend is running!');
});

// Placeholder for future routes
// const courseRoutes = require('./routes/courses');
// app.use('/api/courses', courseRoutes);

app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
