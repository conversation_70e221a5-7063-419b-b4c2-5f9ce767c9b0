<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI教育系统 - 后台管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-800 text-white flex flex-col">
            <div class="px-8 py-6 text-2xl font-bold border-b border-gray-700">
                AI教育后台
            </div>
            <nav class="flex-1 px-4 py-4 space-y-2">
                <a href="admin_dashboard.html" target="content-frame" class="flex items-center px-4 py-2 rounded-md bg-gray-700">
                    <i class="fa-solid fa-tachometer-alt w-6"></i>
                    <span>仪表盘</span>
                </a>
                <a href="admin_courses.html" target="content-frame" class="flex items-center px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fa-solid fa-book w-6"></i>
                    <span>课程管理</span>
                </a>
                <a href="admin_users.html" target="content-frame" class="flex items-center px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fa-solid fa-users w-6"></i>
                    <span>用户管理</span>
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fa-solid fa-cog w-6"></i>
                    <span>系统设置</span>
                </a>
            </nav>
            <div class="px-8 py-4 border-t border-gray-700">
                <a href="admin_login.html" class="flex items-center">
                    <i class="fa-solid fa-sign-out-alt w-6"></i>
                    <span>退出登录</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <header class="bg-white shadow-md p-4 flex justify-between items-center">
                <h1 class="text-xl font-semibold">仪表盘</h1>
                <div class="flex items-center">
                    <span class="mr-4">欢迎, Admin</span>
                    <img src="https://randomuser.me/api/portraits/men/11.jpg" class="w-10 h-10 rounded-full">
                </div>
            </header>
            <main class="flex-1 p-6">
                <iframe name="content-frame" src="admin_dashboard.html" class="w-full h-full border-none"></iframe>
            </main>
        </div>
    </div>
    <script>
        const links = document.querySelectorAll('nav a');
        const headerTitle = document.querySelector('header h1');

        links.forEach(link => {
            link.addEventListener('click', function() {
                links.forEach(l => l.classList.remove('bg-gray-700'));
                this.classList.add('bg-gray-700');
                const title = this.querySelector('span').textContent;
                headerTitle.textContent = title;
            });
        });
    </script>
</body>
</html>
