@echo off
title N8N工作流构建器 - 快速启动

echo.
echo ==========================================
echo    N8N工作流构建器 - 快速启动
echo ==========================================
echo.

REM 检查Node.js
echo [1/4] 检查Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未安装Node.js
    pause
    exit /b 1
)
echo Node.js 检查通过

REM 清理构建缓存
echo.
echo [2/4] 清理缓存...
if exist ".next" rmdir /s /q ".next" 2>nul

REM 安装依赖
echo.
echo [3/4] 检查依赖...
if not exist "node_modules" (
    echo 安装依赖中...
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动服务器
echo.
echo [4/4] 启动开发服务器...
echo.
echo 服务器将在 http://localhost:3000 启动
echo 按 Ctrl+C 停止服务器
echo.

REM 3秒后打开浏览器
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:3000"

REM 启动开发服务器
npm run dev
