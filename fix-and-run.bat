@echo off
chcp 65001 >nul
title N8N工作流构建器 - 修复并运行

echo.
echo ========================================
echo    🔧 N8N工作流构建器 - 修复并运行
echo ========================================
echo.

REM 1. 清理缓存和构建文件
echo [1/5] 清理缓存和构建文件...
if exist ".next" (
    rmdir /s /q ".next" 2>nul
    echo ✅ 清理.next目录
)
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache" 2>nul
    echo ✅ 清理缓存目录
)

REM 2. 检查并修复package.json
echo.
echo [2/5] 检查package.json...
if not exist "package.json" (
    echo 📝 创建package.json...
    (
        echo {
        echo   "name": "n8n-workflow-builder",
        echo   "version": "1.0.0",
        echo   "private": true,
        echo   "scripts": {
        echo     "dev": "next dev",
        echo     "build": "next build",
        echo     "start": "next start"
        echo   },
        echo   "dependencies": {
        echo     "next": "^14.0.0",
        echo     "react": "^18.2.0",
        echo     "react-dom": "^18.2.0"
        echo   },
        echo   "devDependencies": {
        echo     "@types/node": "^20.0.0",
        echo     "@types/react": "^18.2.0",
        echo     "@types/react-dom": "^18.2.0",
        echo     "typescript": "^5.0.0"
        echo   }
        echo }
    ) > package.json
    echo ✅ package.json 创建完成
) else (
    echo ✅ package.json 存在
)

REM 3. 检查并修复next.config.js
echo.
echo [3/5] 检查next.config.js...
if not exist "next.config.js" (
    echo 📝 创建next.config.js...
    (
        echo /** @type {import('next'^}.NextConfig} */
        echo const nextConfig = {
        echo   reactStrictMode: true,
        echo   swcMinify: true
        echo }
        echo.
        echo module.exports = nextConfig
    ) > next.config.js
    echo ✅ next.config.js 创建完成
) else (
    echo ✅ next.config.js 存在
)

REM 4. 重新安装依赖
echo.
echo [4/5] 重新安装依赖...
if exist "node_modules" (
    echo 🗑️  删除旧的node_modules...
    rmdir /s /q "node_modules" 2>nul
)
if exist "package-lock.json" (
    del "package-lock.json" 2>nul
)

echo 📦 安装依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试使用淘宝镜像...
    call npm config set registry https://registry.npmmirror.com
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装仍然失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖安装完成

REM 5. 杀掉可能占用端口的进程
echo.
echo [5/5] 检查并清理端口占用...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
    echo 🔄 终止占用端口3000的进程 %%a
    taskkill /F /PID %%a >nul 2>&1
)

echo.
echo ========================================
echo    🎉 修复完成！正在启动开发服务器...
echo ========================================
echo.

REM 延迟2秒后打开浏览器
timeout /t 2 /nobreak >nul
start "" "http://localhost:3000"

echo 浏览器将自动打开 http://localhost:3000
echo 按 Ctrl+C 可以停止服务器
echo.

REM 启动开发服务器
call npm run dev
