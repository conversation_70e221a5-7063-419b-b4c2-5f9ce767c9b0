# N8N工作流构建器

🚀 **智能化的N8N工作流构建工具** - 通过引导式界面轻松创建自动化流程

## ✨ 特性

- 🎯 **智能规划助手** - 通过问答形式分析需求，生成工作流蓝图
- 🧩 **积木式构建** - 可视化配置N8N节点，无需编写代码
- 📋 **流程蓝图** - 清晰展示工作流执行步骤和节点关系
- 🔧 **参数配置** - 友好的表单界面配置节点参数
- 📥 **一键导出** - 生成标准N8N工作流JSON文件
- 📚 **知识库集成** - 内置API文档、代码示例和最佳实践
- 🎨 **现代化UI** - 基于Bootstrap的响应式设计

## 🎯 使用场景

- **数据同步** - 在不同系统间同步数据
- **消息通知** - 自动发送各类通知消息
- **文件处理** - 自动化文件上传、转换、分析
- **API集成** - 连接不同服务的API
- **监控报警** - 系统状态监控和异常报警
- **报表生成** - 定期生成业务报表

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
# 或
yarn install
```

### 2. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

### 3. 打开浏览器

访问 [http://localhost:3000](http://localhost:3000) 开始使用

## 📖 使用指南

### 第0步：智能规划
1. 选择您的自动化目标（数据同步、消息通知等）
2. 选择触发方式（定时、Webhook、文件监控等）
3. 选择需要集成的服务（企业微信、飞书、数据库等）
4. 系统自动生成工作流蓝图

### 第1步：查看蓝图
- 查看生成的流程蓝图
- 了解推荐的N8N节点
- 查看相关文档和资源链接
- 确认方案后开始构建

### 第2步：配置节点
- 逐个配置每个N8N节点的参数
- 填写必需的配置项（API地址、认证信息等）
- 查看节点文档和代码示例
- 实时验证配置完整性

### 第3步：生成工作流
- 下载生成的N8N工作流JSON文件
- 查看导入N8N的详细指南
- 获取相关资源和文档链接

## 🧩 支持的节点类型

### 触发器节点
- **Webhook** - HTTP请求触发
- **Cron** - 定时触发
- **文件监控** - 文件变化触发
- **邮件触发** - 新邮件触发

### 通讯节点
- **企业微信** - 群机器人消息推送
- **飞书** - 机器人和API集成
- **钉钉** - 机器人消息推送
- **邮件发送** - SMTP邮件发送

### 数据处理节点
- **HTTP Request** - 发送HTTP请求
- **JSON** - JSON数据处理
- **Code** - JavaScript代码执行
- **IF** - 条件判断

### 数据存储节点
- **Google Sheets** - Google表格操作
- **MySQL** - 数据库操作
- **文件系统** - 本地文件读写

## 🛠️ 技术栈

- **前端框架**: Next.js 14 + React 18
- **类型系统**: TypeScript
- **UI框架**: Bootstrap 5 + React Bootstrap
- **图标库**: Lucide React
- **构建工具**: Next.js内置构建系统

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页面
│   └── globals.css        # 全局样式
├── components/            # React组件
│   ├── PlanningWizard.tsx # 规划向导
│   ├── WorkflowBlueprint.tsx # 流程蓝图
│   ├── WorkflowBuilder.tsx # 工作流构建器
│   └── WorkflowResult.tsx # 结果展示
├── data/                  # 数据文件
│   ├── workflowGoals.ts   # 工作流目标数据
│   ├── nodeTemplates.ts   # 节点模板
│   └── knowledgeBase.ts   # 知识库数据
├── types/                 # TypeScript类型定义
│   └── workflow.ts        # 工作流相关类型
└── utils/                 # 工具函数
```

## 🔧 配置说明

### 环境变量
项目不需要特殊的环境变量配置，可以直接运行。

### 自定义配置
- 修改 `src/data/workflowGoals.ts` 添加新的工作流目标
- 修改 `src/data/nodeTemplates.ts` 添加新的节点模板
- 修改 `src/data/knowledgeBase.ts` 添加新的知识库内容

## 📚 知识库

内置丰富的知识库资源：

### 官方文档
- N8N官方文档
- 各服务API文档
- 最佳实践指南

### 代码示例
- 企业微信消息模板
- HTTP请求示例
- JSON数据处理
- 条件判断逻辑

### 教程资源
- 节点配置教程
- 工作流设计模式
- 常见问题解答

## 🚀 部署

### 构建生产版本
```bash
npm run build
```

### 启动生产服务器
```bash
npm start
```

### 静态导出
```bash
npm run build
```
生成的静态文件在 `out/` 目录中，可以部署到任何静态文件服务器。

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发指南
1. Fork本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用 MIT 许可证

## 🙏 致谢

- [N8N](https://n8n.io/) - 强大的工作流自动化工具
- [Next.js](https://nextjs.org/) - React全栈框架
- [Bootstrap](https://getbootstrap.com/) - CSS框架
- [Lucide](https://lucide.dev/) - 图标库

## 📞 支持

如果您在使用过程中遇到问题，可以：

1. 查看N8N官方文档
2. 参考内置的知识库资源
3. 查看代码示例和模板

---

**让自动化变得简单！** 🎉
