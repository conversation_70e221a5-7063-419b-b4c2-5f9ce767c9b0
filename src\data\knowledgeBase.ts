import { KnowledgeItem, CodeSnippet } from '@/types/workflow';

// 知识库资源
export const knowledgeItems: KnowledgeItem[] = [
  // N8N官方资源
  {
    id: 'n8n-docs',
    title: 'N8N官方文档',
    description: 'N8N的完整使用文档，包含所有节点的详细说明',
    category: '官方文档',
    tags: ['文档', '官方', '节点'],
    url: 'https://docs.n8n.io/',
    type: 'documentation'
  },
  {
    id: 'n8n-community',
    title: 'N8N社区论坛',
    description: '与其他N8N用户交流经验，获取帮助和分享工作流',
    category: '社区',
    tags: ['社区', '论坛', '帮助'],
    url: 'https://community.n8n.io/',
    type: 'documentation'
  },
  {
    id: 'n8n-workflows',
    title: 'N8N工作流模板',
    description: '官方提供的工作流模板库，可以直接使用或参考',
    category: '模板',
    tags: ['模板', '示例', '工作流'],
    url: 'https://n8n.io/workflows/',
    type: 'example'
  },

  // 企业微信相关
  {
    id: 'wecom-bot-api',
    title: '企业微信机器人API文档',
    description: '企业微信群机器人的完整API文档和使用指南',
    category: '企业微信',
    tags: ['企业微信', '机器人', 'API'],
    url: 'https://developer.work.weixin.qq.com/document/path/91770',
    type: 'api-reference'
  },
  {
    id: 'wecom-webhook-guide',
    title: '企业微信Webhook配置指南',
    description: '如何创建和配置企业微信群机器人的详细教程',
    category: '企业微信',
    tags: ['企业微信', 'Webhook', '配置'],
    url: 'https://developer.work.weixin.qq.com/tutorial/detail/13376',
    type: 'tutorial'
  },

  // 飞书相关
  {
    id: 'feishu-open-platform',
    title: '飞书开放平台',
    description: '飞书开放平台的完整文档，包含机器人和API集成',
    category: '飞书',
    tags: ['飞书', '开放平台', 'API'],
    url: 'https://open.feishu.cn/document/home/<USER>',
    type: 'api-reference'
  },
  {
    id: 'feishu-bot-guide',
    title: '飞书机器人开发指南',
    description: '如何创建和配置飞书机器人的完整教程',
    category: '飞书',
    tags: ['飞书', '机器人', '开发'],
    url: 'https://open.feishu.cn/document/ukTMukTMukTM/ucTM5YjL3ETO24yNxkjN',
    type: 'tutorial'
  },

  // 钉钉相关
  {
    id: 'dingtalk-open-platform',
    title: '钉钉开放平台',
    description: '钉钉开放平台文档，包含机器人和应用开发',
    category: '钉钉',
    tags: ['钉钉', '开放平台', '机器人'],
    url: 'https://open.dingtalk.com/',
    type: 'api-reference'
  },

  // Google Sheets相关
  {
    id: 'google-sheets-api',
    title: 'Google Sheets API文档',
    description: 'Google Sheets API的完整文档和使用指南',
    category: 'Google',
    tags: ['Google Sheets', 'API', '表格'],
    url: 'https://developers.google.com/sheets/api',
    type: 'api-reference'
  },

  // HTTP和API相关
  {
    id: 'http-methods-guide',
    title: 'HTTP方法详解',
    description: 'HTTP请求方法（GET、POST、PUT、DELETE等）的详细说明',
    category: 'HTTP',
    tags: ['HTTP', 'API', '方法'],
    url: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods',
    type: 'documentation'
  },
  {
    id: 'rest-api-best-practices',
    title: 'REST API最佳实践',
    description: 'RESTful API设计和使用的最佳实践指南',
    category: 'API',
    tags: ['REST', 'API', '最佳实践'],
    url: 'https://restfulapi.net/',
    type: 'documentation'
  },

  // 数据处理相关
  {
    id: 'json-guide',
    title: 'JSON数据格式指南',
    description: 'JSON数据格式的完整介绍和使用技巧',
    category: '数据格式',
    tags: ['JSON', '数据', '格式'],
    url: 'https://www.json.org/json-zh.html',
    type: 'documentation'
  },

  // 定时任务相关
  {
    id: 'cron-expression-guide',
    title: 'Cron表达式详解',
    description: 'Cron表达式的语法和使用方法，用于定时任务配置',
    category: '定时任务',
    tags: ['Cron', '定时', '表达式'],
    url: 'https://crontab.guru/',
    type: 'documentation'
  }
];

// 代码片段库
export const codeSnippets: CodeSnippet[] = [
  {
    id: 'wecom-text-message',
    title: '企业微信文本消息',
    description: '发送简单文本消息到企业微信群',
    language: 'json',
    code: `{
  "msgtype": "text",
  "text": {
    "content": "这是一条测试消息"
  }
}`,
    nodeType: 'n8n-nodes-base.wecom',
    category: '企业微信'
  },
  {
    id: 'wecom-markdown-message',
    title: '企业微信Markdown消息',
    description: '发送Markdown格式的消息到企业微信群',
    language: 'json',
    code: `{
  "msgtype": "markdown",
  "markdown": {
    "content": "## 标题\\n**粗体文本**\\n- 列表项1\\n- 列表项2"
  }
}`,
    nodeType: 'n8n-nodes-base.wecom',
    category: '企业微信'
  },
  {
    id: 'http-get-request',
    title: 'HTTP GET请求',
    description: '发送GET请求获取数据',
    language: 'javascript',
    code: `// 在Code节点中处理HTTP响应
const response = items[0].json;
return [{
  json: {
    status: response.status,
    data: response.data,
    timestamp: new Date().toISOString()
  }
}];`,
    nodeType: 'n8n-nodes-base.httpRequest',
    category: 'HTTP'
  },
  {
    id: 'json-data-processing',
    title: 'JSON数据处理',
    description: '提取和转换JSON数据',
    language: 'javascript',
    code: `// 处理JSON数据
const inputData = items[0].json;
return [{
  json: {
    id: inputData.id,
    name: inputData.user?.name || '未知用户',
    email: inputData.user?.email,
    createdAt: new Date(inputData.timestamp).toLocaleDateString('zh-CN')
  }
}];`,
    nodeType: 'n8n-nodes-base.code',
    category: '数据处理'
  },
  {
    id: 'conditional-logic',
    title: '条件判断逻辑',
    description: '根据条件执行不同的操作',
    language: 'javascript',
    code: `// 条件判断示例
const data = items[0].json;
const priority = data.amount > 1000 ? 'high' : 
                data.amount > 500 ? 'medium' : 'low';

return [{
  json: {
    ...data,
    priority: priority,
    needsApproval: data.amount > 1000
  }
}];`,
    nodeType: 'n8n-nodes-base.code',
    category: '逻辑处理'
  },
  {
    id: 'email-template',
    title: '邮件模板',
    description: '发送格式化的邮件内容',
    language: 'html',
    code: `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通知邮件</title>
</head>
<body>
    <h2>系统通知</h2>
    <p>尊敬的用户，</p>
    <p>您有一个新的任务需要处理：</p>
    <ul>
        <li>任务名称：{{$json.taskName}}</li>
        <li>优先级：{{$json.priority}}</li>
        <li>截止时间：{{$json.deadline}}</li>
    </ul>
    <p>请及时处理。</p>
    <p>系统自动发送，请勿回复。</p>
</body>
</html>`,
    nodeType: 'n8n-nodes-base.emailSend',
    category: '邮件'
  },
  {
    id: 'cron-examples',
    title: 'Cron表达式示例',
    description: '常用的Cron表达式配置',
    language: 'text',
    code: `# 每分钟执行
* * * * *

# 每小时执行（每小时的第0分钟）
0 * * * *

# 每天上午9点执行
0 9 * * *

# 每周一上午9点执行
0 9 * * 1

# 每月1号上午9点执行
0 9 1 * *

# 工作日（周一到周五）上午9点执行
0 9 * * 1-5`,
    nodeType: 'n8n-nodes-base.cron',
    category: '定时任务'
  }
];

// 根据节点类型获取相关资源
export const getResourcesByNodeType = (nodeType: string): KnowledgeItem[] => {
  const typeMapping: Record<string, string[]> = {
    'n8n-nodes-base.wecom': ['企业微信'],
    'n8n-nodes-base.feishu': ['飞书'],
    'n8n-nodes-base.dingtalk': ['钉钉'],
    'n8n-nodes-base.googleSheets': ['Google'],
    'n8n-nodes-base.httpRequest': ['HTTP', 'API'],
    'n8n-nodes-base.json': ['数据格式'],
    'n8n-nodes-base.cron': ['定时任务'],
    'n8n-nodes-base.emailSend': ['邮件']
  };

  const categories = typeMapping[nodeType] || [];
  return knowledgeItems.filter(item => 
    categories.some(category => item.category.includes(category))
  );
};

// 根据节点类型获取代码片段
export const getCodeSnippetsByNodeType = (nodeType: string): CodeSnippet[] => {
  return codeSnippets.filter(snippet => snippet.nodeType === nodeType);
};

// 搜索知识库
export const searchKnowledge = (query: string): KnowledgeItem[] => {
  const lowercaseQuery = query.toLowerCase();
  return knowledgeItems.filter(item =>
    item.title.toLowerCase().includes(lowercaseQuery) ||
    item.description.toLowerCase().includes(lowercaseQuery) ||
    item.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};
