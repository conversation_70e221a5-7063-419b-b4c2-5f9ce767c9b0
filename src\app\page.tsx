'use client';

import React, { useState } from 'react';
import { Container, Navbar, Nav } from 'react-bootstrap';
import { Zap, Github, BookOpen } from 'lucide-react';
import PlanningWizard from '@/components/PlanningWizard';
import WorkflowBlueprint from '@/components/WorkflowBlueprint';
import WorkflowBuilder from '@/components/WorkflowBuilder';
import WorkflowResult from '@/components/WorkflowResult';
import { WorkflowPlan, N8NWorkflow } from '@/types/workflow';

type AppStep = 'planning' | 'blueprint' | 'building' | 'result';

export default function Home() {
  const [currentStep, setCurrentStep] = useState<AppStep>('planning');
  const [workflowPlan, setWorkflowPlan] = useState<WorkflowPlan | null>(null);
  const [generatedWorkflow, setGeneratedWorkflow] = useState<N8NWorkflow | null>(null);

  const handlePlanComplete = (plan: WorkflowPlan) => {
    setWorkflowPlan(plan);
    setCurrentStep('blueprint');
  };

  const handleStartBuilding = () => {
    setCurrentStep('building');
  };

  const handleEditPlan = () => {
    setCurrentStep('planning');
  };

  const handleWorkflowComplete = (workflow: N8NWorkflow) => {
    setGeneratedWorkflow(workflow);
    setCurrentStep('result');
  };

  const handleBackToBuilder = () => {
    setCurrentStep('building');
  };

  const handleStartOver = () => {
    setWorkflowPlan(null);
    setGeneratedWorkflow(null);
    setCurrentStep('planning');
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'planning':
        return <PlanningWizard onPlanComplete={handlePlanComplete} />;
      
      case 'blueprint':
        return workflowPlan ? (
          <WorkflowBlueprint
            plan={workflowPlan}
            onStartBuilding={handleStartBuilding}
            onEditPlan={handleEditPlan}
          />
        ) : null;
      
      case 'building':
        return workflowPlan ? (
          <WorkflowBuilder
            plan={workflowPlan}
            onWorkflowComplete={handleWorkflowComplete}
            onBack={() => setCurrentStep('blueprint')}
          />
        ) : null;
      
      case 'result':
        return generatedWorkflow ? (
          <WorkflowResult
            workflow={generatedWorkflow}
            onBack={handleBackToBuilder}
            onStartOver={handleStartOver}
          />
        ) : null;
      
      default:
        return null;
    }
  };

  return (
    <>
      {/* 导航栏 */}
      <Navbar bg="white" expand="lg" className="shadow-sm">
        <Container>
          <Navbar.Brand href="#" className="d-flex align-items-center">
            <Zap className="text-primary me-2" size={24} />
            <span className="fw-bold">N8N工作流构建器</span>
          </Navbar.Brand>
          
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              <Nav.Link 
                href="https://docs.n8n.io/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="d-flex align-items-center"
              >
                <BookOpen size={16} className="me-1" />
                N8N文档
              </Nav.Link>
              <Nav.Link 
                href="https://github.com/n8n-io/n8n" 
                target="_blank" 
                rel="noopener noreferrer"
                className="d-flex align-items-center"
              >
                <Github size={16} className="me-1" />
                GitHub
              </Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      {/* 主要内容 */}
      <main className="flex-grow-1">
        {renderCurrentStep()}
      </main>

      {/* 页脚 */}
      <footer className="bg-light py-4 mt-5">
        <Container>
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="mb-0 text-muted">
                © 2024 N8N工作流构建器 - 让自动化变得简单
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <small className="text-muted">
                基于 <a href="https://n8n.io/" target="_blank" rel="noopener noreferrer" className="text-decoration-none">N8N</a> 构建
              </small>
            </div>
          </div>
        </Container>
      </footer>
    </>
  );
}
