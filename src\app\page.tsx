'use client';

import React, { useState } from 'react';

export default function Home() {
  const [step, setStep] = useState(1);
  const [selectedGoal, setSelectedGoal] = useState('');
  const [selectedTrigger, setSelectedTrigger] = useState('');

  const goals = [
    { id: 'notification', name: '消息通知', desc: '自动发送通知消息到企业微信、飞书等' },
    { id: 'data-sync', name: '数据同步', desc: '在不同系统之间同步数据' },
    { id: 'file-process', name: '文件处理', desc: '自动处理文件上传、转换等操作' }
  ];

  const triggers = [
    { id: 'webhook', name: 'Webhook触发', desc: 'HTTP请求触发工作流' },
    { id: 'schedule', name: '定时触发', desc: '按时间计划自动执行' },
    { id: 'manual', name: '手动触发', desc: '手动执行工作流' }
  ];

  const generateWorkflow = () => {
    const workflow = {
      name: `${goals.find(g => g.id === selectedGoal)?.name} - ${triggers.find(t => t.id === selectedTrigger)?.name}`,
      nodes: [
        {
          id: 'trigger_node',
          name: triggers.find(t => t.id === selectedTrigger)?.name || '触发器',
          type: selectedTrigger === 'webhook' ? 'n8n-nodes-base.webhook' :
                selectedTrigger === 'schedule' ? 'n8n-nodes-base.cron' : 'n8n-nodes-base.manualTrigger',
          position: [100, 100],
          parameters: selectedTrigger === 'webhook' ? {
            path: 'my-webhook',
            httpMethod: 'POST'
          } : selectedTrigger === 'schedule' ? {
            mode: 'everyHour'
          } : {}
        },
        {
          id: 'action_node',
          name: '执行动作',
          type: selectedGoal === 'notification' ? 'n8n-nodes-base.httpRequest' : 'n8n-nodes-base.code',
          position: [300, 100],
          parameters: selectedGoal === 'notification' ? {
            url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY',
            method: 'POST'
          } : {
            jsCode: '// 处理数据的代码\nreturn items;'
          }
        }
      ],
      connections: {
        [triggers.find(t => t.id === selectedTrigger)?.name || '触发器']: {
          main: [[{ node: '执行动作', type: 'main', index: 0 }]]
        }
      },
      active: false
    };

    const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const cardStyle = {
    padding: '20px',
    margin: '10px 0',
    border: '2px solid #e2e8f0',
    borderRadius: '12px',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    backgroundColor: 'white'
  };

  const selectedCardStyle = {
    ...cardStyle,
    borderColor: '#6366f1',
    backgroundColor: '#f0f0ff',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.15)'
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: '#f8fafc'
    }}>
      {/* 头部 */}
      <div style={{ textAlign: 'center', marginBottom: '40px' }}>
        <h1 style={{
          color: '#6366f1',
          fontSize: '2.5rem',
          marginBottom: '10px',
          fontWeight: 'bold'
        }}>
          🚀 N8N工作流构建器
        </h1>
        <p style={{
          color: '#64748b',
          fontSize: '1.1rem'
        }}>
          通过简单的步骤创建专业的自动化工作流
        </p>
      </div>

      {/* 进度指示器 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginBottom: '40px',
        alignItems: 'center'
      }}>
        {[1, 2, 3].map(num => (
          <React.Fragment key={num}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: step >= num ? '#6366f1' : '#e2e8f0',
              color: step >= num ? 'white' : '#64748b',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 'bold',
              fontSize: '1.1rem'
            }}>
              {step > num ? '✓' : num}
            </div>
            {num < 3 && (
              <div style={{
                width: '60px',
                height: '4px',
                backgroundColor: step > num ? '#6366f1' : '#e2e8f0',
                margin: '0 10px'
              }} />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* 步骤内容 */}
      {step === 1 && (
        <div>
          <h2 style={{ textAlign: 'center', color: '#1e293b', marginBottom: '30px' }}>
            🎯 第1步：选择您的自动化目标
          </h2>
          <div>
            {goals.map(goal => (
              <div
                key={goal.id}
                onClick={() => setSelectedGoal(goal.id)}
                style={selectedGoal === goal.id ? selectedCardStyle : cardStyle}
              >
                <h3 style={{ margin: '0 0 10px 0', color: '#1e293b' }}>{goal.name}</h3>
                <p style={{ margin: 0, color: '#64748b' }}>{goal.desc}</p>
              </div>
            ))}
          </div>
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <button
              onClick={() => setStep(2)}
              disabled={!selectedGoal}
              style={{
                padding: '12px 30px',
                backgroundColor: selectedGoal ? '#6366f1' : '#94a3b8',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: selectedGoal ? 'pointer' : 'not-allowed',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              下一步 →
            </button>
          </div>
        </div>
      )}

      {step === 2 && (
        <div>
          <h2 style={{ textAlign: 'center', color: '#1e293b', marginBottom: '30px' }}>
            ⚡ 第2步：选择触发方式
          </h2>
          <div>
            {triggers.map(trigger => (
              <div
                key={trigger.id}
                onClick={() => setSelectedTrigger(trigger.id)}
                style={selectedTrigger === trigger.id ? selectedCardStyle : cardStyle}
              >
                <h3 style={{ margin: '0 0 10px 0', color: '#1e293b' }}>{trigger.name}</h3>
                <p style={{ margin: 0, color: '#64748b' }}>{trigger.desc}</p>
              </div>
            ))}
          </div>
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <button
              onClick={() => setStep(1)}
              style={{
                padding: '12px 30px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '1rem',
                marginRight: '15px'
              }}
            >
              ← 上一步
            </button>
            <button
              onClick={() => setStep(3)}
              disabled={!selectedTrigger}
              style={{
                padding: '12px 30px',
                backgroundColor: selectedTrigger ? '#6366f1' : '#94a3b8',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: selectedTrigger ? 'pointer' : 'not-allowed',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              生成工作流 →
            </button>
          </div>
        </div>
      )}

      {step === 3 && (
        <div>
          <div style={{ textAlign: 'center', marginBottom: '30px' }}>
            <div style={{ fontSize: '4rem', marginBottom: '20px' }}>🎉</div>
            <h2 style={{ color: '#10b981', marginBottom: '10px' }}>工作流生成完成！</h2>
            <p style={{ color: '#64748b' }}>您的N8N工作流已经准备就绪</p>
          </div>

          <div style={{
            padding: '25px',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '2px solid #10b981',
            marginBottom: '30px'
          }}>
            <h3 style={{ color: '#1e293b', marginBottom: '15px' }}>📋 您的配置：</h3>
            <div style={{ marginBottom: '10px' }}>
              <strong>目标：</strong> {goals.find(g => g.id === selectedGoal)?.name}
            </div>
            <div>
              <strong>触发器：</strong> {triggers.find(t => t.id === selectedTrigger)?.name}
            </div>
          </div>

          <div style={{ textAlign: 'center', marginBottom: '30px' }}>
            <button
              onClick={generateWorkflow}
              style={{
                padding: '15px 40px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                marginRight: '15px'
              }}
            >
              📥 下载工作流文件
            </button>
            <button
              onClick={() => {
                setStep(1);
                setSelectedGoal('');
                setSelectedTrigger('');
              }}
              style={{
                padding: '15px 40px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '1.1rem'
              }}
            >
              🔄 重新开始
            </button>
          </div>

          <div style={{
            padding: '20px',
            backgroundColor: '#fffbeb',
            borderRadius: '8px',
            border: '1px solid #fbbf24'
          }}>
            <h4 style={{ color: '#92400e', marginBottom: '15px' }}>📖 如何使用生成的文件：</h4>
            <ol style={{ color: '#92400e', paddingLeft: '20px' }}>
              <li>打开您的N8N实例</li>
              <li>点击"+"创建新工作流</li>
              <li>点击菜单 → "Import from file"</li>
              <li>选择刚才下载的JSON文件</li>
              <li>配置节点参数并激活工作流</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}
