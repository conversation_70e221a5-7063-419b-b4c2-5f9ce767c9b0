@echo off
chcp 65001 >nul
title N8N工作流构建器 - 开发服务器

echo.
echo ========================================
echo    🚀 N8N工作流构建器 - 开发服务器
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误：未找到package.json文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查node_modules是否存在
if not exist "node_modules" (
    echo 📦 检测到缺少依赖，正在安装...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

REM 检查端口3000是否被占用
echo 🔍 检查端口3000...
netstat -ano | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口3000已被占用，尝试使用端口3001...
    set PORT=3001
) else (
    set PORT=3000
)

echo.
echo 🌟 启动开发服务器...
echo 浏览器将自动打开 http://localhost:%PORT%
echo.
echo 按 Ctrl+C 可以停止服务器
echo ========================================
echo.

REM 延迟3秒后打开浏览器
timeout /t 3 /nobreak >nul
start "" "http://localhost:%PORT%"

REM 启动开发服务器
if "%PORT%"=="3001" (
    call npx next dev -p 3001
) else (
    call npm run dev
)
