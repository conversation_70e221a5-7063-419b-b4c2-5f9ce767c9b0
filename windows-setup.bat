@echo off
echo 正在创建N8N工作流构建器项目...

REM 创建目录结构
mkdir src\app 2>nul
mkdir src\components 2>nul
mkdir src\data 2>nul
mkdir src\types 2>nul
mkdir examples 2>nul

echo 目录结构创建完成！

REM 创建package.json
echo {> package.json
echo   "name": "n8n-workflow-builder",>> package.json
echo   "version": "1.0.0",>> package.json
echo   "private": true,>> package.json
echo   "scripts": {>> package.json
echo     "dev": "next dev",>> package.json
echo     "build": "next build",>> package.json
echo     "start": "next start">> package.json
echo   },>> package.json
echo   "dependencies": {>> package.json
echo     "next": "^14.0.0",>> package.json
echo     "react": "^18.2.0",>> package.json
echo     "react-dom": "^18.2.0",>> package.json
echo     "bootstrap": "^5.3.0",>> package.json
echo     "react-bootstrap": "^2.9.0",>> package.json
echo     "lucide-react": "^0.294.0">> package.json
echo   },>> package.json
echo   "devDependencies": {>> package.json
echo     "@types/node": "^20.0.0",>> package.json
echo     "@types/react": "^18.2.0",>> package.json
echo     "@types/react-dom": "^18.2.0",>> package.json
echo     "typescript": "^5.0.0",>> package.json
echo     "eslint": "^8.0.0",>> package.json
echo     "eslint-config-next": "^14.0.0">> package.json
echo   }>> package.json
echo }>> package.json

echo package.json 创建完成！

REM 创建tsconfig.json
echo {> tsconfig.json
echo   "compilerOptions": {>> tsconfig.json
echo     "target": "es5",>> tsconfig.json
echo     "lib": ["dom", "dom.iterable", "es6"],>> tsconfig.json
echo     "allowJs": true,>> tsconfig.json
echo     "skipLibCheck": true,>> tsconfig.json
echo     "strict": true,>> tsconfig.json
echo     "noEmit": true,>> tsconfig.json
echo     "esModuleInterop": true,>> tsconfig.json
echo     "module": "esnext",>> tsconfig.json
echo     "moduleResolution": "bundler",>> tsconfig.json
echo     "resolveJsonModule": true,>> tsconfig.json
echo     "isolatedModules": true,>> tsconfig.json
echo     "jsx": "preserve",>> tsconfig.json
echo     "incremental": true,>> tsconfig.json
echo     "plugins": [{"name": "next"}],>> tsconfig.json
echo     "baseUrl": ".",>> tsconfig.json
echo     "paths": {"@/*": ["./src/*"]}>> tsconfig.json
echo   },>> tsconfig.json
echo   "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],>> tsconfig.json
echo   "exclude": ["node_modules"]>> tsconfig.json
echo }>> tsconfig.json

echo tsconfig.json 创建完成！

REM 创建next.config.js
echo /** @type {import('next').NextConfig} */> next.config.js
echo const nextConfig = {>> next.config.js
echo   reactStrictMode: true,>> next.config.js
echo   swcMinify: true>> next.config.js
echo }>> next.config.js
echo.>> next.config.js
echo module.exports = nextConfig>> next.config.js

echo next.config.js 创建完成！

echo.
echo 项目基础结构创建完成！
echo 请运行以下命令继续：
echo 1. npm install
echo 2. npm run dev
echo.
pause
