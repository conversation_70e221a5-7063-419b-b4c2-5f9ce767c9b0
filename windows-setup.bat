@echo off
chcp 65001 >nul
title N8N工作流构建器 - 自动安装程序

echo.
echo ========================================
echo    🚀 N8N工作流构建器 自动安装程序
echo ========================================
echo.

REM 检查Node.js是否安装
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到npm，请重新安装Node.js
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

REM 创建目录结构
echo.
echo [2/6] 创建项目目录结构...
mkdir src\app 2>nul
mkdir src\components 2>nul
mkdir src\data 2>nul
mkdir src\types 2>nul
mkdir examples 2>nul
echo ✅ 目录结构创建完成

REM 创建package.json
echo.
echo [3/6] 创建package.json...
(
echo {
echo   "name": "n8n-workflow-builder",
echo   "version": "1.0.0",
echo   "private": true,
echo   "scripts": {
echo     "dev": "next dev",
echo     "build": "next build",
echo     "start": "next start",
echo     "lint": "next lint"
echo   },
echo   "dependencies": {
echo     "next": "^14.0.0",
echo     "react": "^18.2.0",
echo     "react-dom": "^18.2.0"
echo   },
echo   "devDependencies": {
echo     "@types/node": "^20.0.0",
echo     "@types/react": "^18.2.0",
echo     "@types/react-dom": "^18.2.0",
echo     "typescript": "^5.0.0"
echo   }
echo }
) > package.json
echo ✅ package.json 创建完成

REM 创建next.config.js
echo.
echo [4/6] 创建next.config.js...
(
echo /** @type {import('next'^}.NextConfig} */
echo const nextConfig = {
echo   reactStrictMode: true,
echo   swcMinify: true
echo }
echo.
echo module.exports = nextConfig
) > next.config.js
echo ✅ next.config.js 创建完成

REM 创建基础页面文件
echo.
echo [5/6] 创建基础页面文件...

REM 创建layout.tsx
(
echo export default function RootLayout^({
echo   children,
echo }: {
echo   children: React.ReactNode
echo }^) {
echo   return ^(
echo     ^<html lang="zh-CN"^>
echo       ^<head^>
echo         ^<title^>N8N工作流构建器^</title^>
echo         ^<meta charSet="utf-8" /^>
echo         ^<meta name="viewport" content="width=device-width, initial-scale=1" /^>
echo       ^</head^>
echo       ^<body style={{
echo         margin: 0,
echo         fontFamily: 'Arial, sans-serif',
echo         backgroundColor: '#f8fafc'
echo       }}^>
echo         {children}
echo       ^</body^>
echo     ^</html^>
echo   ^)
echo }
) > src\app\layout.tsx

REM 创建简化的page.tsx
(
echo 'use client';
echo.
echo import React, { useState } from 'react';
echo.
echo export default function Home^(^) {
echo   const [step, setStep] = useState^(1^);
echo   const [selectedGoal, setSelectedGoal] = useState^(''^);
echo   const [selectedTrigger, setSelectedTrigger] = useState^(''^);
echo.
echo   const goals = [
echo     { id: 'notification', name: '消息通知', desc: '自动发送通知消息' },
echo     { id: 'data-sync', name: '数据同步', desc: '在不同系统之间同步数据' },
echo     { id: 'file-process', name: '文件处理', desc: '自动处理文件操作' }
echo   ];
echo.
echo   const triggers = [
echo     { id: 'webhook', name: 'Webhook触发', desc: 'HTTP请求触发工作流' },
echo     { id: 'schedule', name: '定时触发', desc: '按时间计划自动执行' },
echo     { id: 'manual', name: '手动触发', desc: '手动执行工作流' }
echo   ];
echo.
echo   return ^(
echo     ^<div style={{ minHeight: '100vh', padding: '20px', maxWidth: '800px', margin: '0 auto' }}^>
echo       ^<h1 style={{ textAlign: 'center', color: '#6366f1', fontSize: '2.5rem' }}^>
echo         🚀 N8N工作流构建器
echo       ^</h1^>
echo       ^<p style={{ textAlign: 'center', color: '#64748b' }}^>
echo         通过简单的步骤创建专业的自动化工作流
echo       ^</p^>
echo       ^<div style={{ textAlign: 'center', marginTop: '50px' }}^>
echo         ^<p style={{ fontSize: '1.2rem', color: '#10b981' }}^>
echo           ✅ 项目安装成功！开发服务器正在运行...
echo         ^</p^>
echo         ^<p style={{ color: '#64748b' }}^>
echo           您可以开始开发您的N8N工作流构建器了
echo         ^</p^>
echo       ^</div^>
echo     ^</div^>
echo   ^);
echo }
) > src\app\page.tsx

echo ✅ 基础页面文件创建完成

REM 安装依赖
echo.
echo [6/6] 安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo ========================================
echo    🎉 安装完成！正在启动开发服务器...
echo ========================================
echo.
echo 浏览器将自动打开 http://localhost:3000
echo 如果没有自动打开，请手动访问该地址
echo.
echo 按 Ctrl+C 可以停止开发服务器
echo.

REM 启动开发服务器
start "" "http://localhost:3000"
call npm run dev
