"""
AutoGen编程工作流 - 三个Agent协作编程
包含：代码编写Agent、代码审查Agent、代码优化Agent

基于AutoGen最新版本的API和最佳实践
"""

import asyncio
import json
from typing import List, Dict, Any
from pathlib import Path

# AutoGen核心导入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.tools import FunctionTool


class ProgrammingWorkflow:
    """编程工作流管理类"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4o"):
        """
        初始化编程工作流
        
        Args:
            api_key: OpenAI API密钥，如果为None则从环境变量获取
            model: 使用的模型名称
        """
        self.model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,  # 如果为None，会自动从OPENAI_API_KEY环境变量获取
            temperature=0.7
        )
        
        # 创建工作目录
        self.work_dir = Path("coding_workspace")
        self.work_dir.mkdir(exist_ok=True)
        
        # 初始化agents
        self._setup_agents()
        self._setup_team()
    
    def _setup_agents(self):
        """设置三个专业化的agents"""
        
        # Agent 1: 代码编写专家
        self.coder_agent = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            description="专业的代码编写专家，负责根据需求编写高质量的代码",
            system_message="""你是一个专业的代码编写专家。你的职责是：

1. 仔细分析用户需求，理解功能要求
2. 编写清晰、可读、高效的代码
3. 遵循最佳编程实践和代码规范
4. 添加适当的注释和文档字符串
5. 考虑错误处理和边界情况
6. 使用合适的设计模式和架构

编写完代码后，请说明你的设计思路和关键实现点。
完成后请说 'CODE_READY' 以便进入审查阶段。"""
        )
        
        # Agent 2: 代码审查专家
        self.reviewer_agent = AssistantAgent(
            name="CodeReviewer", 
            model_client=self.model_client,
            description="经验丰富的代码审查专家，负责审查代码质量和提出改进建议",
            system_message="""你是一个经验丰富的代码审查专家。你的职责是：

1. 仔细审查代码的正确性、可读性和性能
2. 检查是否遵循编程最佳实践
3. 识别潜在的bug、安全问题和性能瓶颈
4. 评估代码的可维护性和可扩展性
5. 提出具体的改进建议和优化方案
6. 检查错误处理和边界情况的处理

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 安全性考虑

完成审查后请说 'REVIEW_COMPLETE' 以便进入优化阶段。"""
        )
        
        # Agent 3: 代码优化专家
        self.optimizer_agent = AssistantAgent(
            name="CodeOptimizer",
            model_client=self.model_client, 
            description="代码优化专家，负责根据审查建议优化和重构代码",
            system_message="""你是一个代码优化专家。你的职责是：

1. 分析原始代码和审查建议
2. 实施代码优化和重构
3. 提升代码性能、可读性和可维护性
4. 修复发现的bug和安全问题
5. 优化算法和数据结构
6. 改进错误处理和异常管理
7. 添加或改进测试用例

请提供优化后的完整代码，并说明：
- 实施的优化措施
- 性能改进点
- 代码质量提升
- 修复的问题

完成优化后请说 'OPTIMIZATION_COMPLETE'。"""
        )
    
    def _setup_team(self):
        """设置团队和终止条件"""
        # 设置终止条件：当优化完成时结束
        self.termination_condition = (
            TextMentionTermination("OPTIMIZATION_COMPLETE") | 
            MaxMessageTermination(max_messages=15)  # 防止无限循环
        )
        
        # 创建轮询式团队聊天
        self.team = RoundRobinGroupChat(
            participants=[self.coder_agent, self.reviewer_agent, self.optimizer_agent],
            termination_condition=self.termination_condition
        )
    
    async def run_programming_task(self, task_description: str) -> Dict[str, Any]:
        """
        运行编程任务
        
        Args:
            task_description: 编程任务描述
            
        Returns:
            包含任务结果的字典
        """
        print(f"🚀 开始编程工作流: {task_description}")
        print("=" * 60)
        
        # 运行团队协作
        result = await Console(self.team.run_stream(task=task_description))
        
        # 保存工作流状态
        await self._save_workflow_state(task_description, result)
        
        return {
            "task": task_description,
            "messages": result.messages,
            "status": "completed",
            "participants": [agent.name for agent in self.team.participants]
        }
    
    async def _save_workflow_state(self, task: str, result):
        """保存工作流状态到文件"""
        state_file = self.work_dir / f"workflow_state_{hash(task) % 10000}.json"
        
        # 保存团队状态
        team_state = await self.team.save_state()
        
        workflow_data = {
            "task": task,
            "timestamp": str(asyncio.get_event_loop().time()),
            "team_state": team_state,
            "message_count": len(result.messages) if hasattr(result, 'messages') else 0
        }
        
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(workflow_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 工作流状态已保存到: {state_file}")
    
    async def load_workflow_state(self, state_file: str):
        """从文件加载工作流状态"""
        with open(state_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        await self.team.load_state(workflow_data['team_state'])
        print(f"📂 工作流状态已从 {state_file} 加载")
        
        return workflow_data
    
    async def reset_workflow(self):
        """重置工作流状态"""
        await self.team.reset()
        print("🔄 工作流已重置")
    
    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
        print("🔌 模型客户端连接已关闭")


# 示例使用函数
async def example_usage():
    """示例使用方法"""
    
    # 创建编程工作流实例
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务1：实现一个简单的数据结构
        task1 = """
        请实现一个Python的优先队列(Priority Queue)类，要求：
        1. 支持插入元素和优先级
        2. 支持弹出最高优先级元素
        3. 支持查看队列大小和是否为空
        4. 包含适当的错误处理
        5. 添加完整的文档字符串
        """
        
        print("📋 任务1: 实现优先队列")
        result1 = await workflow.run_programming_task(task1)
        
        # 重置工作流准备下一个任务
        await workflow.reset_workflow()
        
        # 示例任务2：实现一个算法
        task2 = """
        请实现一个高效的字符串匹配算法（如KMP算法），要求：
        1. 实现完整的KMP字符串匹配算法
        2. 包含预处理函数构建失败函数
        3. 支持查找所有匹配位置
        4. 添加时间复杂度分析
        5. 包含测试用例
        """
        
        print("\n📋 任务2: 实现KMP字符串匹配算法")
        result2 = await workflow.run_programming_task(task2)
        
        print("\n✅ 所有编程任务完成!")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
    
    finally:
        # 关闭连接
        await workflow.close()


# 主函数
async def main():
    """主函数"""
    print("🎯 AutoGen编程工作流演示")
    print("=" * 60)
    
    await example_usage()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
