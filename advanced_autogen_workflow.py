"""
高级AutoGen编程工作流 - 包含工具函数和图形化流程控制
使用GraphFlow实现更精确的agent交互控制

基于AutoGen最新版本的API和最佳实践
"""

import asyncio
import json
import os
import subprocess
from typing import List, Dict, Any, Optional
from pathlib import Path

# AutoGen核心导入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.tools import FunctionTool


# 工具函数定义
def save_code_to_file(filename: str, code: str, language: str = "python") -> str:
    """
    保存代码到文件
    
    Args:
        filename: 文件名
        code: 代码内容
        language: 编程语言
        
    Returns:
        保存结果信息
    """
    try:
        work_dir = Path("coding_workspace")
        work_dir.mkdir(exist_ok=True)
        
        # 根据语言确定文件扩展名
        extensions = {
            "python": ".py",
            "javascript": ".js", 
            "java": ".java",
            "cpp": ".cpp",
            "c": ".c"
        }
        
        ext = extensions.get(language.lower(), ".txt")
        if not filename.endswith(ext):
            filename += ext
            
        file_path = work_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(code)
            
        return f"代码已成功保存到: {file_path}"
        
    except Exception as e:
        return f"保存代码时出错: {str(e)}"


def run_code_tests(filename: str) -> str:
    """
    运行代码测试
    
    Args:
        filename: 要测试的文件名
        
    Returns:
        测试结果
    """
    try:
        work_dir = Path("coding_workspace")
        file_path = work_dir / filename
        
        if not file_path.exists():
            return f"文件 {filename} 不存在"
        
        # 对于Python文件，尝试运行语法检查
        if filename.endswith('.py'):
            result = subprocess.run(
                ['python', '-m', 'py_compile', str(file_path)],
                capture_output=True,
                text=True,
                cwd=work_dir
            )
            
            if result.returncode == 0:
                return f"✅ {filename} 语法检查通过"
            else:
                return f"❌ {filename} 语法错误:\n{result.stderr}"
        
        return f"文件 {filename} 已检查，请手动验证功能"
        
    except Exception as e:
        return f"运行测试时出错: {str(e)}"


def analyze_code_complexity(code: str) -> str:
    """
    分析代码复杂度
    
    Args:
        code: 代码内容
        
    Returns:
        复杂度分析结果
    """
    try:
        lines = code.split('\n')
        total_lines = len(lines)
        code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        
        # 简单的复杂度指标
        complexity_indicators = {
            'if': code.count('if '),
            'for': code.count('for '),
            'while': code.count('while '),
            'def': code.count('def '),
            'class': code.count('class ')
        }
        
        analysis = f"""
📊 代码复杂度分析:
- 总行数: {total_lines}
- 代码行数: {code_lines}
- 注释行数: {comment_lines}
- 注释率: {comment_lines/total_lines*100:.1f}%

🔍 复杂度指标:
"""
        for indicator, count in complexity_indicators.items():
            analysis += f"- {indicator}语句: {count}\n"
            
        return analysis
        
    except Exception as e:
        return f"分析代码复杂度时出错: {str(e)}"


class AdvancedProgrammingWorkflow:
    """高级编程工作流管理类"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4o"):
        """初始化高级编程工作流"""
        self.model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,
            temperature=0.7
        )
        
        # 创建工作目录
        self.work_dir = Path("coding_workspace")
        self.work_dir.mkdir(exist_ok=True)
        
        # 创建工具
        self._setup_tools()
        
        # 初始化agents
        self._setup_agents()
        
        # 设置图形化工作流
        self._setup_graph_workflow()
    
    def _setup_tools(self):
        """设置工具函数"""
        self.save_code_tool = FunctionTool(
            save_code_to_file,
            description="保存代码到文件，支持多种编程语言"
        )
        
        self.test_code_tool = FunctionTool(
            run_code_tests,
            description="运行代码测试和语法检查"
        )
        
        self.analyze_complexity_tool = FunctionTool(
            analyze_code_complexity,
            description="分析代码复杂度和质量指标"
        )
    
    def _setup_agents(self):
        """设置专业化的agents"""
        
        # Agent 1: 高级代码编写专家（带工具）
        self.coder_agent = AssistantAgent(
            name="AdvancedCoder",
            model_client=self.model_client,
            tools=[self.save_code_tool, self.analyze_complexity_tool],
            description="高级代码编写专家，具备代码保存和复杂度分析能力",
            system_message="""你是一个高级代码编写专家。你的职责是：

1. 深入分析需求，设计优雅的解决方案
2. 编写高质量、可维护的代码
3. 使用工具保存代码到文件
4. 使用工具分析代码复杂度
5. 遵循SOLID原则和设计模式
6. 考虑性能优化和内存管理

编写代码时请：
- 先分析需求和设计思路
- 编写完整的代码实现
- 使用save_code_to_file工具保存代码
- 使用analyze_code_complexity工具分析复杂度
- 说明关键设计决策

完成后请说 'CODING_COMPLETE'。"""
        )
        
        # Agent 2: 高级代码审查专家（带工具）
        self.reviewer_agent = AssistantAgent(
            name="AdvancedReviewer",
            model_client=self.model_client,
            tools=[self.test_code_tool, self.analyze_complexity_tool],
            description="高级代码审查专家，具备测试和复杂度分析能力",
            system_message="""你是一个高级代码审查专家。你的职责是：

1. 全面审查代码质量、安全性和性能
2. 使用工具运行代码测试
3. 分析代码复杂度和可维护性
4. 识别设计模式的使用是否恰当
5. 评估算法效率和空间复杂度
6. 检查错误处理和边界情况

审查流程：
- 仔细阅读代码逻辑
- 使用test_code_tool运行测试
- 使用analyze_code_complexity分析复杂度
- 提供详细的审查报告
- 给出具体的改进建议

完成审查后请说 'REVIEW_COMPLETE'。"""
        )
        
        # Agent 3: 高级代码优化专家（带工具）
        self.optimizer_agent = AssistantAgent(
            name="AdvancedOptimizer",
            model_client=self.model_client,
            tools=[self.save_code_tool, self.test_code_tool, self.analyze_complexity_tool],
            description="高级代码优化专家，具备完整的代码处理工具链",
            system_message="""你是一个高级代码优化专家。你的职责是：

1. 基于审查建议进行代码重构和优化
2. 提升代码性能、可读性和可维护性
3. 实施最佳实践和设计模式
4. 优化算法和数据结构
5. 改进错误处理和异常管理

优化流程：
- 分析原始代码和审查建议
- 实施优化和重构
- 使用save_code_tool保存优化后的代码
- 使用test_code_tool验证代码正确性
- 使用analyze_code_complexity分析改进效果
- 提供优化报告

完成优化后请说 'OPTIMIZATION_COMPLETE'。"""
        )
    
    def _setup_graph_workflow(self):
        """设置图形化工作流"""
        # 创建有向图构建器
        builder = DiGraphBuilder()
        
        # 添加节点
        builder.add_node(self.coder_agent)
        builder.add_node(self.reviewer_agent) 
        builder.add_node(self.optimizer_agent)
        
        # 定义工作流边：Coder -> Reviewer -> Optimizer
        builder.add_edge(self.coder_agent, self.reviewer_agent)
        builder.add_edge(self.reviewer_agent, self.optimizer_agent)
        
        # 构建图
        self.graph = builder.build()
        
        # 创建图形化流程
        self.flow = GraphFlow(
            participants=builder.get_participants(),
            graph=self.graph
        )
    
    async def run_advanced_task(self, task_description: str) -> Dict[str, Any]:
        """运行高级编程任务"""
        print(f"🚀 开始高级编程工作流: {task_description}")
        print("=" * 80)
        
        # 运行图形化流程
        result = await Console(self.flow.run_stream(task=task_description))
        
        # 保存结果
        await self._save_results(task_description, result)
        
        return {
            "task": task_description,
            "status": "completed",
            "workflow_type": "graph_flow",
            "participants": [agent.name for agent in self.flow.participants]
        }
    
    async def _save_results(self, task: str, result):
        """保存工作流结果"""
        results_file = self.work_dir / f"workflow_results_{hash(task) % 10000}.json"
        
        results_data = {
            "task": task,
            "timestamp": str(asyncio.get_event_loop().time()),
            "workflow_type": "advanced_graph_flow",
            "status": "completed"
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 工作流结果已保存到: {results_file}")
    
    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
        print("🔌 模型客户端连接已关闭")


# 示例使用
async def advanced_example():
    """高级工作流示例"""
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 复杂编程任务
        task = """
        请设计并实现一个高性能的LRU缓存系统，要求：
        
        1. 核心功能：
           - 支持get(key)和put(key, value)操作
           - 自动淘汰最近最少使用的项目
           - 支持设置最大容量
        
        2. 性能要求：
           - get和put操作时间复杂度为O(1)
           - 空间复杂度为O(capacity)
        
        3. 高级特性：
           - 线程安全
           - 支持过期时间设置
           - 提供缓存统计信息（命中率等）
           - 支持序列化和反序列化
        
        4. 代码质量：
           - 完整的错误处理
           - 详细的文档字符串
           - 单元测试用例
           - 性能基准测试
        """
        
        result = await workflow.run_advanced_task(task)
        print(f"\n✅ 高级编程任务完成: {result['status']}")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
    
    finally:
        await workflow.close()


if __name__ == "__main__":
    print("🎯 高级AutoGen编程工作流演示")
    print("=" * 80)
    asyncio.run(advanced_example())
