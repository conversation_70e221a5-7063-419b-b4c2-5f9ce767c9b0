<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-black">
    <div class="text-white">
        <!-- Video Player -->
        <div class="relative aspect-video bg-black">
            <!-- Using a static image as a placeholder for the video -->
            <img src="https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover" alt="Video thumbnail">
            <div class="absolute inset-0 flex items-center justify-center">
                <button class="text-white bg-black/50 w-16 h-16 rounded-full flex items-center justify-center">
                    <i class="fa-solid fa-play text-3xl ml-1"></i>
                </button>
            </div>
            <div class="absolute top-4 left-4 bg-black/30 w-8 h-8 rounded-full flex items-center justify-center">
                <i class="fa-solid fa-chevron-left"></i>
            </div>
        </div>

        <div class="bg-gray-800 p-4">
            <h1 class="text-xl font-bold">1-1 Vue 3 核心概念</h1>
            <p class="text-sm text-gray-400">正在播放: 第1节</p>
        </div>

        <!-- Tabs for Playlist and Q&A -->
        <div class="bg-gray-900">
            <div class="flex border-b border-gray-700">
                <button class="flex-1 py-3 text-center border-b-2 border-blue-500 text-blue-500">课程目录</button>
                <button class="flex-1 py-3 text-center text-gray-400">AI问答</button>
            </div>

            <!-- Playlist -->
            <div class="p-4 space-y-3">
                <div class="flex items-center justify-between p-3 rounded-lg bg-gray-700/50">
                    <div>
                        <p class="font-semibold text-blue-400">1-1 Vue 3 核心概念</p>
                        <span class="text-xs text-gray-400"><i class="fa-solid fa-play-circle text-blue-400"></i> 正在播放 (15:30)</span>
                    </div>
                    <i class="fa-solid fa-circle-check text-green-500"></i>
                </div>
                <div class="flex items-center justify-between p-3 rounded-lg">
                    <div>
                        <p>1-2 组件化开发思想</p>
                        <span class="text-xs text-gray-500"><i class="fa-regular fa-clock"></i> 22:10</span>
                    </div>
                    <i class="fa-solid fa-lock text-gray-500"></i>
                </div>
                <div class="flex items-center justify-between p-3 rounded-lg">
                    <div>
                        <p>2-1 Pinia状态管理</p>
                        <span class="text-xs text-gray-500"><i class="fa-regular fa-clock"></i> 18:45</span>
                    </div>
                    <i class="fa-solid fa-lock text-gray-500"></i>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
