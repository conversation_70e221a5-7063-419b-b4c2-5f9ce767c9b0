<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div>
        <!-- Course Header Image -->
        <div class="relative">
            <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?q=80&w=1200&auto=format&fit=crop" class="w-full h-60 object-cover">
            <div class="absolute top-4 left-4 bg-black/30 text-white w-8 h-8 rounded-full flex items-center justify-center">
                <i class="fa-solid fa-chevron-left"></i>
            </div>
        </div>

        <div class="bg-white p-4 -mt-10 rounded-t-2xl">
            <!-- Title and Price -->
            <div class="flex justify-between items-start mb-2">
                <h1 class="text-2xl font-bold text-gray-800">Vue 3 企业级项目实战</h1>
                <span class="text-2xl font-bold text-red-500">¥299</span>
            </div>
            <p class="text-sm text-gray-500 mb-4">从零开始，带你构建一个功能完备的企业级应用。</p>

            <!-- Instructor Info -->
            <div class="flex items-center mb-6">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" class="w-10 h-10 rounded-full mr-3">
                <div>
                    <p class="font-semibold text-gray-800">王老师</p>
                    <p class="text-xs text-gray-500">资深前端架构师</p>
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b mb-4">
                <button class="flex-1 py-2 text-center border-b-2 border-blue-500 text-blue-500 font-semibold">课程介绍</button>
                <button class="flex-1 py-2 text-center text-gray-500">课程目录</button>
            </div>

            <!-- Course Content -->
            <div>
                <h3 class="font-bold text-lg mb-2">你将学到什么？</h3>
                <ul class="space-y-2 text-gray-600 text-sm list-disc list-inside">
                    <li>Vue 3 核心概念与新特性</li>
                    <li>Pinia 状态管理</li>
                    <li>Vue Router 4 路由管理</li>
                    <li>TypeScript 在 Vue 中的应用</li>
                    <li>Element Plus 组件库使用</li>
                    <li>项目部署与优化</li>
                </ul>
            </div>
        </div>
        
        <!-- Fixed Bottom Bar -->
        <div class="sticky bottom-0 bg-white p-4 border-t flex items-center justify-between">
            <button class="text-gray-600 flex flex-col items-center">
                <i class="fa-regular fa-heart text-xl"></i>
                <span class="text-xs">收藏</span>
            </button>
            <button class="bg-blue-500 text-white font-bold py-3 px-10 rounded-full">立即购买</button>
        </div>
    </div>
</body>
</html>
