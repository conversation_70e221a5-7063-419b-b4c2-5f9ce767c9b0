import type { Metadata } from 'next'
import 'bootstrap/dist/css/bootstrap.min.css'
import './globals.css'

export const metadata: Metadata = {
  title: 'N8N工作流构建器',
  description: '智能化的N8N工作流构建工具，通过引导式界面轻松创建自动化流程',
  keywords: ['N8N', '工作流', '自动化', '无代码', '构建器'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className="bg-light">
        <div className="min-vh-100 d-flex flex-column">
          {children}
        </div>
      </body>
    </html>
  )
}
