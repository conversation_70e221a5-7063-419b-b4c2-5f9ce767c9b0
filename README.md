# AutoGen编程工作流

基于Microsoft AutoGen框架的智能编程工作流系统，实现了三个专业化Agent的协作编程模式：代码编写、代码审查、代码优化。

## 🎬 快速体验

```bash
# 1. 克隆或下载项目文件
# 2. 安装依赖和设置环境
python setup.py

# 3. 设置API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 4. 快速体验
python quick_start.py
```

## 🌟 特性

- **三Agent协作模式**: 代码编写 → 代码审查 → 代码优化
- **基于最新AutoGen API**: 使用AutoGen v0.4+的最新特性
- **图形化工作流控制**: 使用GraphFlow实现精确的Agent交互
- **丰富的工具集成**: 代码保存、测试运行、复杂度分析
- **多语言支持**: Python、JavaScript、Java、C++等
- **状态管理**: 支持工作流状态保存和恢复
- **可配置化**: 灵活的配置选项和模板系统

## 📁 文件结构

```
├── autogen_programming_workflow.py    # 基础工作流实现
├── advanced_autogen_workflow.py       # 高级工作流（带工具和图形控制）
├── config.py                         # 配置文件
├── README.md                         # 使用说明
└── coding_workspace/                 # 工作目录（自动创建）
    ├── results/                      # 结果文件
    └── logs/                        # 日志文件
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装AutoGen
pip install autogen-agentchat[openai]
pip install autogen-ext[openai]

# 设置OpenAI API密钥
export OPENAI_API_KEY="your-api-key-here"
```

### 2. 基础使用

```python
import asyncio
from autogen_programming_workflow import ProgrammingWorkflow

async def main():
    # 创建工作流实例
    workflow = ProgrammingWorkflow()
    
    # 定义编程任务
    task = """
    请实现一个Python的栈(Stack)数据结构，要求：
    1. 支持push、pop、peek操作
    2. 支持检查是否为空和获取大小
    3. 包含完整的错误处理
    4. 添加详细的文档字符串
    """
    
    # 运行工作流
    result = await workflow.run_programming_task(task)
    
    # 关闭连接
    await workflow.close()

# 运行
asyncio.run(main())
```

### 3. 高级使用（带工具）

```python
import asyncio
from advanced_autogen_workflow import AdvancedProgrammingWorkflow

async def main():
    # 创建高级工作流实例
    workflow = AdvancedProgrammingWorkflow()
    
    # 复杂编程任务
    task = """
    请设计并实现一个高性能的LRU缓存系统，要求：
    1. O(1)时间复杂度的get和put操作
    2. 线程安全
    3. 支持过期时间
    4. 提供缓存统计
    """
    
    # 运行高级工作流
    result = await workflow.run_advanced_task(task)
    
    await workflow.close()

asyncio.run(main())
```

## 🔧 配置选项

### 环境变量

```bash
# OpenAI API配置
export OPENAI_API_KEY="your-api-key"

# 可选配置
export AUTOGEN_MODEL="gpt-4o"           # 默认模型
export AUTOGEN_TEMPERATURE="0.7"        # 温度参数
export AUTOGEN_MAX_MESSAGES="15"        # 最大消息数
```

### 配置文件

修改 `config.py` 中的配置选项：

```python
class WorkflowConfig:
    DEFAULT_MODEL = "gpt-4o"
    TEMPERATURE = 0.7
    MAX_MESSAGES = 15
    TIMEOUT_SECONDS = 300
```

## 🤖 Agent角色说明

### 1. CodeWriter (代码编写专家)
- **职责**: 根据需求编写高质量代码
- **特点**: 遵循最佳实践，考虑错误处理
- **工具**: 代码保存、复杂度分析

### 2. CodeReviewer (代码审查专家)
- **职责**: 审查代码质量，提出改进建议
- **特点**: 关注安全性、性能、可维护性
- **工具**: 代码测试、复杂度分析

### 3. CodeOptimizer (代码优化专家)
- **职责**: 根据审查建议优化代码
- **特点**: 性能优化、重构、bug修复
- **工具**: 完整工具链（保存、测试、分析）

## 📊 工作流程

```mermaid
graph LR
    A[用户需求] --> B[CodeWriter]
    B --> C[CodeReviewer]
    C --> D[CodeOptimizer]
    D --> E[最终代码]
    
    B -.-> F[保存代码]
    B -.-> G[分析复杂度]
    C -.-> H[运行测试]
    D -.-> I[优化验证]
```

## 🛠️ 工具函数

### 代码保存工具
```python
save_code_to_file(filename, code, language)
```

### 代码测试工具
```python
run_code_tests(filename)
```

### 复杂度分析工具
```python
analyze_code_complexity(code)
```

## 📝 任务模板

系统提供多种预定义任务模板：

### 数据结构实现
```python
from config import get_task_from_template

task = get_task_from_template(
    "data_structure",
    data_structure_name="二叉搜索树"
)
```

### 算法实现
```python
task = get_task_from_template(
    "algorithm", 
    algorithm_name="快速排序"
)
```

### Web API设计
```python
task = get_task_from_template(
    "web_api",
    api_name="用户管理"
)
```

## 🔄 状态管理

### 保存工作流状态
```python
# 自动保存（在任务完成后）
result = await workflow.run_programming_task(task)

# 手动保存
state = await workflow.team.save_state()
```

### 加载工作流状态
```python
# 从文件加载
workflow_data = await workflow.load_workflow_state("state_file.json")

# 恢复状态
await workflow.team.load_state(workflow_data['team_state'])
```

### 重置工作流
```python
await workflow.reset_workflow()
```

## 🎯 使用示例

### 示例1：实现数据结构
```python
task = """
请实现一个Python的优先队列，要求：
1. 支持插入元素和优先级
2. 支持弹出最高优先级元素
3. 支持查看队列大小
4. 包含错误处理
"""
```

### 示例2：算法实现
```python
task = """
请实现KMP字符串匹配算法，要求：
1. 完整的KMP实现
2. 构建失败函数
3. 支持查找所有匹配
4. 时间复杂度分析
"""
```

### 示例3：系统设计
```python
task = """
请设计一个分布式缓存系统，要求：
1. 支持多节点部署
2. 数据一致性保证
3. 故障恢复机制
4. 性能监控
"""
```

## 🚨 注意事项

1. **API密钥**: 确保设置了有效的OpenAI API密钥
2. **网络连接**: 需要稳定的网络连接访问OpenAI API
3. **资源使用**: 复杂任务可能消耗较多API调用次数
4. **文件权限**: 确保有写入工作目录的权限
5. **依赖版本**: 使用AutoGen v0.4+版本

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🔗 相关链接

- [AutoGen官方文档](https://microsoft.github.io/autogen/)
- [AutoGen GitHub仓库](https://github.com/microsoft/autogen)
- [OpenAI API文档](https://platform.openai.com/docs)
