'use client';

import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Badge, Al<PERSON>, ListGroup } from 'react-bootstrap';
import { ArrowRight, Play, Settings, ExternalLink, Download, Edit } from 'lucide-react';
import { WorkflowPlan } from '@/types/workflow';

interface WorkflowBlueprintProps {
  plan: WorkflowPlan;
  onStartBuilding: () => void;
  onEditPlan: () => void;
}

const WorkflowBlueprint: React.FC<WorkflowBlueprintProps> = ({ 
  plan, 
  onStartBuilding, 
  onEditPlan 
}) => {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'success';
      case 'medium': return 'warning';
      case 'complex': return 'danger';
      default: return 'secondary';
    }
  };

  const getComplexityText = (complexity: string) => {
    switch (complexity) {
      case 'simple': return '简单';
      case 'medium': return '中等';
      case 'complex': return '复杂';
      default: return '未知';
    }
  };

  const generateWorkflowDescription = () => {
    const { goal, trigger, services } = plan;
    const serviceNames = services.map(s => s.name).join('、');
    
    return `当${trigger.name.replace('触发', '')}时，通过${serviceNames}来${goal.description}。`;
  };

  const estimateTime = () => {
    const baseTime = {
      'simple': 15,
      'medium': 30,
      'complex': 60
    };
    
    const serviceMultiplier = Math.max(1, plan.services.length * 0.5);
    const estimatedMinutes = baseTime[plan.estimatedComplexity] * serviceMultiplier;
    
    return Math.round(estimatedMinutes);
  };

  return (
    <Container className="py-4">
      <div className="text-center mb-4">
        <h2 className="text-primary mb-2">🎯 工作流蓝图</h2>
        <p className="text-muted">基于您的需求，我们为您生成了以下工作流方案</p>
      </div>

      {/* 概览卡片 */}
      <Card className="mb-4 shadow-sm">
        <Card.Header className="bg-light">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="mb-0">📋 方案概览</h5>
            <Badge bg={getComplexityColor(plan.estimatedComplexity)}>
              {getComplexityText(plan.estimatedComplexity)}
            </Badge>
          </div>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={8}>
              <h6 className="text-primary">{plan.goal.title}</h6>
              <p className="mb-3">{generateWorkflowDescription()}</p>
              
              <div className="d-flex flex-wrap gap-2 mb-3">
                <Badge bg="info" className="d-flex align-items-center">
                  {plan.trigger.icon} {plan.trigger.name}
                </Badge>
                {plan.services.map(service => (
                  <Badge key={service.id} bg="secondary" className="d-flex align-items-center">
                    {service.icon} {service.name}
                  </Badge>
                ))}
              </div>
            </Col>
            <Col md={4}>
              <div className="text-center">
                <div className="mb-2">
                  <small className="text-muted">预计配置时间</small>
                  <div className="h4 text-primary">{estimateTime()} 分钟</div>
                </div>
                <div>
                  <small className="text-muted">涉及节点</small>
                  <div className="h5 text-success">{plan.recommendedNodes.length} 个</div>
                </div>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* 流程步骤 */}
      <Card className="mb-4 shadow-sm">
        <Card.Header className="bg-light">
          <h5 className="mb-0">🔄 执行流程</h5>
        </Card.Header>
        <Card.Body>
          <div className="workflow-flow">
            {plan.steps.map((step, index) => (
              <div key={step.id} className="d-flex align-items-center mb-3">
                <div className="step-indicator active me-3">
                  {index + 1}
                </div>
                <div className="flex-grow-1">
                  <Card className="border-0 bg-light">
                    <Card.Body className="py-2">
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="mb-1">{step.title}</h6>
                          <small className="text-muted">{step.description}</small>
                        </div>
                        <Badge bg="outline-primary" className="ms-2">
                          {step.nodeType.split('.').pop()}
                        </Badge>
                      </div>
                    </Card.Body>
                  </Card>
                </div>
                {index < plan.steps.length - 1 && (
                  <ArrowRight className="text-muted ms-3" size={20} />
                )}
              </div>
            ))}
          </div>
        </Card.Body>
      </Card>

      {/* 推荐节点 */}
      <Card className="mb-4 shadow-sm">
        <Card.Header className="bg-light">
          <h5 className="mb-0">🧩 推荐的N8N节点</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            {plan.recommendedNodes.map((nodeType, index) => (
              <Col md={6} lg={4} key={index} className="mb-3">
                <Card className="h-100 border-primary border-opacity-25">
                  <Card.Body className="text-center">
                    <div className="mb-2">
                      <Settings className="text-primary" size={24} />
                    </div>
                    <h6 className="mb-1">{nodeType.split('.').pop()}</h6>
                    <small className="text-muted">{nodeType}</small>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Card.Body>
      </Card>

      {/* 相关资源 */}
      <Card className="mb-4 shadow-sm">
        <Card.Header className="bg-light">
          <h5 className="mb-0">📚 相关资源</h5>
        </Card.Header>
        <Card.Body>
          <ListGroup variant="flush">
            {plan.services.map(service => (
              <ListGroup.Item key={service.id} className="d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  <span className="me-2">{service.icon}</span>
                  <div>
                    <strong>{service.name}</strong>
                    <div className="small text-muted">API文档和集成指南</div>
                  </div>
                </div>
                <Button 
                  variant="outline-primary" 
                  size="sm"
                  href={service.documentationUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink size={14} className="me-1" />
                  查看文档
                </Button>
              </ListGroup.Item>
            ))}
            
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <div className="d-flex align-items-center">
                <span className="me-2">📖</span>
                <div>
                  <strong>N8N官方文档</strong>
                  <div className="small text-muted">节点使用说明和最佳实践</div>
                </div>
              </div>
              <Button 
                variant="outline-primary" 
                size="sm"
                href="https://docs.n8n.io/"
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink size={14} className="me-1" />
                查看文档
              </Button>
            </ListGroup.Item>
          </ListGroup>
        </Card.Body>
      </Card>

      {/* 注意事项 */}
      <Alert variant="info" className="mb-4">
        <Alert.Heading className="h6">💡 配置提示</Alert.Heading>
        <ul className="mb-0">
          <li>确保您有相关服务的访问权限和API密钥</li>
          <li>建议先在测试环境中验证工作流</li>
          <li>复杂的工作流可能需要额外的错误处理节点</li>
          {plan.services.some(s => s.authRequired) && (
            <li>部分服务需要OAuth认证，请提前准备相关凭据</li>
          )}
        </ul>
      </Alert>

      {/* 操作按钮 */}
      <div className="d-flex justify-content-center gap-3">
        <Button variant="outline-secondary" onClick={onEditPlan}>
          <Edit size={16} className="me-1" />
          修改规划
        </Button>
        
        <Button variant="primary" size="lg" onClick={onStartBuilding}>
          <Play size={16} className="me-1" />
          开始构建工作流
        </Button>
      </div>
    </Container>
  );
};

export default WorkflowBlueprint;
