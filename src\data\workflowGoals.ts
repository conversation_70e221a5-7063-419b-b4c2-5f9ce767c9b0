import { WorkflowGoal, TriggerType, ServiceIntegration } from '@/types/workflow';

// 工作流目标数据
export const workflowGoals: WorkflowGoal[] = [
  {
    id: 'data-sync',
    title: '数据同步',
    description: '在不同系统之间同步数据，保持信息一致性',
    category: 'data-sync',
    complexity: 'medium'
  },
  {
    id: 'notification',
    title: '消息通知',
    description: '当特定事件发生时，自动发送通知给相关人员',
    category: 'notification',
    complexity: 'simple'
  },
  {
    id: 'file-processing',
    title: '文件处理',
    description: '自动处理文件上传、转换、分析等操作',
    category: 'automation',
    complexity: 'medium'
  },
  {
    id: 'api-integration',
    title: 'API集成',
    description: '连接不同的API服务，实现数据交换和功能整合',
    category: 'integration',
    complexity: 'complex'
  },
  {
    id: 'monitoring',
    title: '监控报警',
    description: '监控系统状态、性能指标，异常时自动报警',
    category: 'monitoring',
    complexity: 'medium'
  },
  {
    id: 'report-generation',
    title: '报表生成',
    description: '定期生成各类业务报表和统计分析',
    category: 'automation',
    complexity: 'complex'
  }
];

// 触发器类型数据
export const triggerTypes: TriggerType[] = [
  {
    id: 'schedule',
    name: '定时触发',
    description: '按照设定的时间间隔或特定时间执行',
    nodeType: 'n8n-nodes-base.cron',
    icon: '⏰',
    category: 'schedule'
  },
  {
    id: 'webhook',
    name: 'Webhook触发',
    description: '通过HTTP请求触发工作流执行',
    nodeType: 'n8n-nodes-base.webhook',
    icon: '🔗',
    category: 'webhook'
  },
  {
    id: 'file-watch',
    name: '文件监控',
    description: '监控文件或文件夹的变化',
    nodeType: 'n8n-nodes-base.fileWatcher',
    icon: '📁',
    category: 'file'
  },
  {
    id: 'email-trigger',
    name: '邮件触发',
    description: '收到新邮件时触发',
    nodeType: 'n8n-nodes-base.emailReadImap',
    icon: '📧',
    category: 'email'
  },
  {
    id: 'database-trigger',
    name: '数据库触发',
    description: '数据库数据变化时触发',
    nodeType: 'n8n-nodes-base.mysqlTrigger',
    icon: '🗄️',
    category: 'database'
  },
  {
    id: 'manual',
    name: '手动触发',
    description: '手动执行工作流',
    nodeType: 'n8n-nodes-base.manualTrigger',
    icon: '👆',
    category: 'webhook'
  }
];

// 服务集成数据
export const serviceIntegrations: ServiceIntegration[] = [
  {
    id: 'wecom',
    name: '企业微信',
    description: '企业微信机器人消息推送',
    category: '通讯协作',
    nodeTypes: ['n8n-nodes-base.wecom'],
    authRequired: false,
    documentationUrl: 'https://developer.work.weixin.qq.com/document/path/91770',
    icon: '💬'
  },
  {
    id: 'feishu',
    name: '飞书',
    description: '飞书机器人和API集成',
    category: '通讯协作',
    nodeTypes: ['n8n-nodes-base.feishu'],
    authRequired: true,
    documentationUrl: 'https://open.feishu.cn/document/home/<USER>',
    icon: '🚀'
  },
  {
    id: 'dingtalk',
    name: '钉钉',
    description: '钉钉机器人和开放平台',
    category: '通讯协作',
    nodeTypes: ['n8n-nodes-base.dingtalk'],
    authRequired: false,
    documentationUrl: 'https://open.dingtalk.com/',
    icon: '📱'
  },
  {
    id: 'google-sheets',
    name: 'Google Sheets',
    description: 'Google表格数据读写',
    category: '数据存储',
    nodeTypes: ['n8n-nodes-base.googleSheets'],
    authRequired: true,
    documentationUrl: 'https://developers.google.com/sheets/api',
    icon: '📊'
  },
  {
    id: 'mysql',
    name: 'MySQL数据库',
    description: 'MySQL数据库操作',
    category: '数据存储',
    nodeTypes: ['n8n-nodes-base.mysql'],
    authRequired: true,
    documentationUrl: 'https://dev.mysql.com/doc/',
    icon: '🗄️'
  },
  {
    id: 'http-request',
    name: 'HTTP请求',
    description: '发送HTTP请求到任意API',
    category: 'API集成',
    nodeTypes: ['n8n-nodes-base.httpRequest'],
    authRequired: false,
    documentationUrl: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/',
    icon: '🌐'
  },
  {
    id: 'email',
    name: '邮件发送',
    description: 'SMTP邮件发送',
    category: '通讯协作',
    nodeTypes: ['n8n-nodes-base.emailSend'],
    authRequired: true,
    documentationUrl: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.emailsend/',
    icon: '📧'
  },
  {
    id: 'file-system',
    name: '文件系统',
    description: '本地文件读写操作',
    category: '文件处理',
    nodeTypes: ['n8n-nodes-base.readBinaryFile', 'n8n-nodes-base.writeBinaryFile'],
    authRequired: false,
    documentationUrl: 'https://docs.n8n.io/integrations/builtin/core-nodes/',
    icon: '📁'
  },
  {
    id: 'json',
    name: 'JSON处理',
    description: 'JSON数据解析和处理',
    category: '数据处理',
    nodeTypes: ['n8n-nodes-base.json'],
    authRequired: false,
    documentationUrl: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.json/',
    icon: '📋'
  },
  {
    id: 'code',
    name: '代码执行',
    description: 'JavaScript/Python代码执行',
    category: '数据处理',
    nodeTypes: ['n8n-nodes-base.code'],
    authRequired: false,
    documentationUrl: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/',
    icon: '💻'
  }
];
