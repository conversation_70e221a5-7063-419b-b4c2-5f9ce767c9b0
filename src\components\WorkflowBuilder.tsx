'use client';

import React, { useState } from 'react';
import { Container, <PERSON>, Col, Card, But<PERSON>, Form, Badge, Alert, Modal, ListGroup } from 'react-bootstrap';
import { Plus, Settings, Trash2, ArrowRight, Save, Download, Eye, ExternalLink } from 'lucide-react';
import { WorkflowPlan, NodeTemplate, UserConfiguration, N8NWorkflow } from '@/types/workflow';
import { nodeTemplates } from '@/data/nodeTemplates';

interface WorkflowBuilderProps {
  plan: WorkflowPlan;
  onWorkflowComplete: (workflow: N8NWorkflow) => void;
  onBack: () => void;
}

interface ConfiguredNode {
  id: string;
  template: NodeTemplate;
  configuration: Record<string, any>;
  position: [number, number];
}

const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({ 
  plan, 
  onWorkflowComplete, 
  onBack 
}) => {
  const [configuredNodes, setConfiguredNodes] = useState<ConfiguredNode[]>([]);
  const [currentNodeIndex, setCurrentNodeIndex] = useState(0);
  const [showNodeConfig, setShowNodeConfig] = useState(false);
  const [selectedNodeTemplate, setSelectedNodeTemplate] = useState<NodeTemplate | null>(null);
  const [nodeConfiguration, setNodeConfiguration] = useState<Record<string, any>>({});

  // 初始化推荐节点
  React.useEffect(() => {
    if (configuredNodes.length === 0 && plan.recommendedNodes.length > 0) {
      const initialNodes = plan.recommendedNodes.map((nodeType, index) => ({
        id: `node_${index}`,
        template: nodeTemplates[nodeType] || {
          type: nodeType,
          name: nodeType.split('.').pop() || 'Unknown',
          description: '未知节点类型',
          category: '其他',
          icon: '⚙️',
          parameters: [],
          documentation: ''
        },
        configuration: {},
        position: [100 + index * 200, 100] as [number, number]
      }));
      setConfiguredNodes(initialNodes);
    }
  }, [plan.recommendedNodes, configuredNodes.length]);

  const handleConfigureNode = (nodeIndex: number) => {
    const node = configuredNodes[nodeIndex];
    setSelectedNodeTemplate(node.template);
    setNodeConfiguration(node.configuration);
    setCurrentNodeIndex(nodeIndex);
    setShowNodeConfig(true);
  };

  const handleSaveNodeConfiguration = () => {
    if (selectedNodeTemplate) {
      const updatedNodes = [...configuredNodes];
      updatedNodes[currentNodeIndex] = {
        ...updatedNodes[currentNodeIndex],
        configuration: nodeConfiguration
      };
      setConfiguredNodes(updatedNodes);
      setShowNodeConfig(false);
      setNodeConfiguration({});
    }
  };

  const handleParameterChange = (paramName: string, value: any) => {
    setNodeConfiguration(prev => ({
      ...prev,
      [paramName]: value
    }));
  };

  const isNodeConfigured = (node: ConfiguredNode) => {
    const requiredParams = node.template.parameters.filter(p => p.required);
    return requiredParams.every(param => 
      node.configuration[param.name] !== undefined && 
      node.configuration[param.name] !== ''
    );
  };

  const canGenerateWorkflow = () => {
    return configuredNodes.length > 0 && configuredNodes.every(isNodeConfigured);
  };

  const generateN8NWorkflow = (): N8NWorkflow => {
    const nodes = configuredNodes.map((node, index) => ({
      id: node.id,
      name: `${node.template.name} ${index + 1}`,
      type: node.template.type,
      typeVersion: 1,
      position: node.position,
      parameters: node.configuration,
      ...(node.template.credentials && { credentials: {} })
    }));

    // 生成简单的线性连接
    const connections: Record<string, Record<string, any[][]>> = {};
    for (let i = 0; i < nodes.length - 1; i++) {
      const currentNode = nodes[i];
      const nextNode = nodes[i + 1];
      connections[currentNode.name] = {
        main: [[{ node: nextNode.name, type: 'main', index: 0 }]]
      };
    }

    return {
      name: `${plan.goal.title} - 自动生成`,
      nodes,
      connections,
      active: false,
      settings: {}
    };
  };

  const handleGenerateWorkflow = () => {
    try {
      const workflow = generateN8NWorkflow();
      onWorkflowComplete(workflow);
    } catch (error) {
      console.error('生成工作流失败:', error);
    }
  };

  const renderParameterInput = (param: any) => {
    const value = nodeConfiguration[param.name] || param.default || '';

    switch (param.type) {
      case 'string':
        return (
          <Form.Control
            type="text"
            value={value}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            placeholder={param.placeholder}
            required={param.required}
          />
        );
      
      case 'number':
        return (
          <Form.Control
            type="number"
            value={value}
            onChange={(e) => handleParameterChange(param.name, Number(e.target.value))}
            placeholder={param.placeholder}
            required={param.required}
          />
        );
      
      case 'boolean':
        return (
          <Form.Check
            type="checkbox"
            checked={Boolean(value)}
            onChange={(e) => handleParameterChange(param.name, e.target.checked)}
          />
        );
      
      case 'options':
        return (
          <Form.Select
            value={value}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            required={param.required}
          >
            <option value="">请选择...</option>
            {param.options?.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.name}
              </option>
            ))}
          </Form.Select>
        );
      
      default:
        return (
          <Form.Control
            as="textarea"
            rows={3}
            value={value}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            placeholder={param.placeholder}
            required={param.required}
          />
        );
    }
  };

  const renderNodeConfigModal = () => (
    <Modal show={showNodeConfig} onHide={() => setShowNodeConfig(false)} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          <div className="d-flex align-items-center">
            <span className="me-2">{selectedNodeTemplate?.icon}</span>
            配置 {selectedNodeTemplate?.name}
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {selectedNodeTemplate && (
          <>
            <Alert variant="info" className="mb-3">
              <strong>节点说明：</strong> {selectedNodeTemplate.description}
            </Alert>
            
            <Form>
              {selectedNodeTemplate.parameters.map((param) => (
                <Form.Group key={param.name} className="mb-3">
                  <Form.Label>
                    {param.displayName}
                    {param.required && <span className="text-danger">*</span>}
                  </Form.Label>
                  {renderParameterInput(param)}
                  {param.description && (
                    <Form.Text className="text-muted">{param.description}</Form.Text>
                  )}
                </Form.Group>
              ))}
            </Form>

            {selectedNodeTemplate.credentials && (
              <Alert variant="warning" className="mt-3">
                <strong>注意：</strong> 此节点需要配置认证凭据。请在N8N中设置相应的凭据。
              </Alert>
            )}
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={() => setShowNodeConfig(false)}>
          取消
        </Button>
        <Button 
          variant="primary" 
          onClick={handleSaveNodeConfiguration}
          disabled={!selectedNodeTemplate?.parameters.filter(p => p.required).every(p => 
            nodeConfiguration[p.name] !== undefined && nodeConfiguration[p.name] !== ''
          )}
        >
          <Save size={16} className="me-1" />
          保存配置
        </Button>
      </Modal.Footer>
    </Modal>
  );

  return (
    <Container className="py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="text-primary mb-1">🧩 工作流构建器</h2>
          <p className="text-muted mb-0">配置您的N8N节点参数</p>
        </div>
        <Button variant="outline-secondary" onClick={onBack}>
          返回蓝图
        </Button>
      </div>

      {/* 进度指示器 */}
      <Card className="mb-4">
        <Card.Body>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h6 className="mb-1">配置进度</h6>
              <small className="text-muted">
                已配置 {configuredNodes.filter(isNodeConfigured).length} / {configuredNodes.length} 个节点
              </small>
            </div>
            <div className="text-end">
              <div className="h4 text-primary mb-0">
                {configuredNodes.length > 0 ? 
                  Math.round((configuredNodes.filter(isNodeConfigured).length / configuredNodes.length) * 100) : 0
                }%
              </div>
            </div>
          </div>
          <div className="progress mt-2" style={{ height: '6px' }}>
            <div 
              className="progress-bar bg-primary" 
              style={{ 
                width: `${configuredNodes.length > 0 ? 
                  (configuredNodes.filter(isNodeConfigured).length / configuredNodes.length) * 100 : 0
                }%` 
              }}
            />
          </div>
        </Card.Body>
      </Card>

      {/* 节点配置列表 */}
      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header className="bg-light">
              <h5 className="mb-0">🔧 节点配置</h5>
            </Card.Header>
            <Card.Body>
              {configuredNodes.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <Settings size={48} className="text-muted" />
                  </div>
                  <h6 className="text-muted">暂无节点</h6>
                  <p className="text-muted">请等待系统加载推荐节点...</p>
                </div>
              ) : (
                <div className="workflow-nodes">
                  {configuredNodes.map((node, index) => (
                    <div key={node.id} className="mb-3">
                      <Card className={`node-card ${isNodeConfigured(node) ? 'border-success' : 'border-warning'}`}>
                        <Card.Body>
                          <div className="d-flex justify-content-between align-items-center">
                            <div className="d-flex align-items-center">
                              <div className="me-3">
                                <div className="step-indicator active">
                                  {index + 1}
                                </div>
                              </div>
                              <div>
                                <div className="d-flex align-items-center mb-1">
                                  <span className="me-2">{node.template.icon}</span>
                                  <h6 className="mb-0">{node.template.name}</h6>
                                  <Badge 
                                    bg={isNodeConfigured(node) ? 'success' : 'warning'} 
                                    className="ms-2"
                                  >
                                    {isNodeConfigured(node) ? '已配置' : '待配置'}
                                  </Badge>
                                </div>
                                <small className="text-muted">{node.template.description}</small>
                              </div>
                            </div>
                            <div className="d-flex gap-2">
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => handleConfigureNode(index)}
                              >
                                <Settings size={14} className="me-1" />
                                配置
                              </Button>
                              <Button
                                variant="outline-info"
                                size="sm"
                                href={node.template.documentation}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <ExternalLink size={14} />
                              </Button>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                      
                      {index < configuredNodes.length - 1 && (
                        <div className="text-center my-2">
                          <ArrowRight className="text-muted" size={20} />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm sticky-top">
            <Card.Header className="bg-light">
              <h5 className="mb-0">📊 工作流信息</h5>
            </Card.Header>
            <Card.Body>
              <ListGroup variant="flush">
                <ListGroup.Item className="d-flex justify-content-between">
                  <span>工作流名称</span>
                  <strong>{plan.goal.title}</strong>
                </ListGroup.Item>
                <ListGroup.Item className="d-flex justify-content-between">
                  <span>节点数量</span>
                  <strong>{configuredNodes.length}</strong>
                </ListGroup.Item>
                <ListGroup.Item className="d-flex justify-content-between">
                  <span>已配置节点</span>
                  <strong>{configuredNodes.filter(isNodeConfigured).length}</strong>
                </ListGroup.Item>
                <ListGroup.Item className="d-flex justify-content-between">
                  <span>复杂度</span>
                  <Badge bg={
                    plan.estimatedComplexity === 'simple' ? 'success' :
                    plan.estimatedComplexity === 'medium' ? 'warning' : 'danger'
                  }>
                    {plan.estimatedComplexity === 'simple' ? '简单' :
                     plan.estimatedComplexity === 'medium' ? '中等' : '复杂'}
                  </Badge>
                </ListGroup.Item>
              </ListGroup>

              <hr />

              <div className="d-grid gap-2">
                <Button
                  variant="success"
                  size="lg"
                  onClick={handleGenerateWorkflow}
                  disabled={!canGenerateWorkflow()}
                >
                  <Download size={16} className="me-1" />
                  生成工作流文件
                </Button>
                
                {!canGenerateWorkflow() && (
                  <small className="text-muted text-center">
                    请完成所有必需节点的配置
                  </small>
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {renderNodeConfigModal()}
    </Container>
  );
};

export default WorkflowBuilder;
