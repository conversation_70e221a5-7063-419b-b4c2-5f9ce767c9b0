'use client';

import React, { useState } from 'react';

export default function Home() {
  const [step, setStep] = useState(1);
  const [selectedGoal, setSelectedGoal] = useState('');
  const [selectedTrigger, setSelectedTrigger] = useState('');

  const goals = [
    { id: 'notification', name: '消息通知', desc: '自动发送通知消息' },
    { id: 'data-sync', name: '数据同步', desc: '同步不同系统的数据' },
    { id: 'file-process', name: '文件处理', desc: '自动处理文件操作' }
  ];

  const triggers = [
    { id: 'webhook', name: 'Webhook触发', desc: 'HTTP请求触发' },
    { id: 'schedule', name: '定时触发', desc: '按时间计划执行' },
    { id: 'manual', name: '手动触发', desc: '手动执行工作流' }
  ];

  const generateWorkflow = () => {
    const workflow = {
      name: `${selectedGoal} - ${selectedTrigger}`,
      nodes: [
        {
          id: 'trigger',
          name: selectedTrigger,
          type: selectedTrigger === 'webhook' ? 'n8n-nodes-base.webhook' : 'n8n-nodes-base.cron'
        },
        {
          id: 'action',
          name: '执行动作',
          type: 'n8n-nodes-base.httpRequest'
        }
      ]
    };

    const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'workflow.json';
    a.click();
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#6366f1', textAlign: 'center' }}>🚀 N8N工作流构建器</h1>
      
      {step === 1 && (
        <div>
          <h2>第1步：选择您的目标</h2>
          <div style={{ display: 'grid', gap: '10px', marginTop: '20px' }}>
            {goals.map(goal => (
              <div 
                key={goal.id}
                onClick={() => setSelectedGoal(goal.id)}
                style={{
                  padding: '15px',
                  border: selectedGoal === goal.id ? '2px solid #6366f1' : '1px solid #ddd',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  backgroundColor: selectedGoal === goal.id ? '#f0f0ff' : 'white'
                }}
              >
                <h3>{goal.name}</h3>
                <p>{goal.desc}</p>
              </div>
            ))}
          </div>
          <button 
            onClick={() => setStep(2)}
            disabled={!selectedGoal}
            style={{
              marginTop: '20px',
              padding: '10px 20px',
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: selectedGoal ? 'pointer' : 'not-allowed'
            }}
          >
            下一步
          </button>
        </div>
      )}

      {step === 2 && (
        <div>
          <h2>第2步：选择触发方式</h2>
          <div style={{ display: 'grid', gap: '10px', marginTop: '20px' }}>
            {triggers.map(trigger => (
              <div 
                key={trigger.id}
                onClick={() => setSelectedTrigger(trigger.id)}
                style={{
                  padding: '15px',
                  border: selectedTrigger === trigger.id ? '2px solid #6366f1' : '1px solid #ddd',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  backgroundColor: selectedTrigger === trigger.id ? '#f0f0ff' : 'white'
                }}
              >
                <h3>{trigger.name}</h3>
                <p>{trigger.desc}</p>
              </div>
            ))}
          </div>
          <div style={{ marginTop: '20px' }}>
            <button 
              onClick={() => setStep(1)}
              style={{
                padding: '10px 20px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                marginRight: '10px'
              }}
            >
              上一步
            </button>
            <button 
              onClick={() => setStep(3)}
              disabled={!selectedTrigger}
              style={{
                padding: '10px 20px',
                backgroundColor: '#6366f1',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: selectedTrigger ? 'pointer' : 'not-allowed'
              }}
            >
              生成工作流
            </button>
          </div>
        </div>
      )}

      {step === 3 && (
        <div>
          <h2>🎉 工作流生成完成！</h2>
          <div style={{ 
            padding: '20px', 
            backgroundColor: '#f0f9ff', 
            borderRadius: '8px',
            marginTop: '20px'
          }}>
            <h3>您的配置：</h3>
            <p><strong>目标：</strong> {goals.find(g => g.id === selectedGoal)?.name}</p>
            <p><strong>触发器：</strong> {triggers.find(t => t.id === selectedTrigger)?.name}</p>
          </div>
          
          <div style={{ marginTop: '20px' }}>
            <button 
              onClick={generateWorkflow}
              style={{
                padding: '15px 30px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                fontSize: '16px',
                marginRight: '10px'
              }}
            >
              📥 下载工作流文件
            </button>
            <button 
              onClick={() => { setStep(1); setSelectedGoal(''); setSelectedTrigger(''); }}
              style={{
                padding: '15px 30px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                fontSize: '16px'
              }}
            >
              重新开始
            </button>
          </div>

          <div style={{ 
            marginTop: '30px',
            padding: '15px',
            backgroundColor: '#fffbeb',
            borderRadius: '8px',
            border: '1px solid #fbbf24'
          }}>
            <h4>📖 如何使用生成的文件：</h4>
            <ol>
              <li>打开您的N8N实例</li>
              <li>点击"+"创建新工作流</li>
              <li>点击菜单 → "Import from file"</li>
              <li>选择刚才下载的JSON文件</li>
              <li>配置节点参数并激活工作流</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}
