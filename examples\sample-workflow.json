{"name": "企业微信通知工作流 - 示例", "nodes": [{"id": "webhook_trigger", "name": "Webhook触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 100], "parameters": {"path": "notification-webhook", "httpMethod": "POST", "responseMode": "onReceived"}}, {"id": "data_processor", "name": "数据处理", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [300, 100], "parameters": {"mode": "runOnceForAllItems", "jsCode": "// 处理接收到的数据\nconst inputData = items[0].json;\n\n// 格式化消息内容\nconst message = `📢 系统通知\\n\\n**事件类型**: ${inputData.eventType || '未知'}\\n**时间**: ${new Date().toLocaleString('zh-CN')}\\n**详情**: ${inputData.message || '无详细信息'}\\n\\n---\\n*此消息由系统自动发送*`;\n\nreturn [{\n  json: {\n    message: message,\n    originalData: inputData,\n    timestamp: new Date().toISOString()\n  }\n}];"}}, {"id": "condition_check", "name": "条件判断", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [500, 100], "parameters": {"conditions": {"string": [{"value1": "={{$json.originalData.priority}}", "operation": "equal", "value2": "high"}]}, "combineOperation": "any"}}, {"id": "wecom_notification", "name": "企业微信通知", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [700, 100], "parameters": {"url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "msgtype", "value": "markdown"}, {"name": "markdown", "value": {"content": "={{$json.message}}"}}]}, "options": {}}}, {"id": "email_backup", "name": "邮件备份", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [700, 300], "parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "系统通知备份 - {{$json.originalData.eventType}}", "text": "{{$json.message}}", "options": {}}, "credentials": {"smtp": {"id": "smtp_credentials", "name": "SMTP邮箱配置"}}}], "connections": {"Webhook触发器": {"main": [[{"node": "数据处理", "type": "main", "index": 0}]]}, "数据处理": {"main": [[{"node": "条件判断", "type": "main", "index": 0}]]}, "条件判断": {"main": [[{"node": "企业微信通知", "type": "main", "index": 0}], [{"node": "邮件备份", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "Asia/Shanghai", "saveManualExecutions": true}, "staticData": {}, "meta": {"templateCredsSetupCompleted": false, "description": "这是一个示例工作流，展示了如何接收Webhook数据，处理后发送到企业微信群，并根据优先级决定是否发送邮件备份。", "tags": ["示例", "企业微信", "通知", "Webhook"]}}