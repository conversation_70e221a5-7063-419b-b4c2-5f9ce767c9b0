// N8N工作流相关类型定义

export interface N8NNode {
  id: string;
  name: string;
  type: string;
  typeVersion: number;
  position: [number, number];
  parameters: Record<string, any>;
  credentials?: Record<string, string>;
}

export interface N8NConnection {
  node: string;
  type: string;
  index: number;
}

export interface N8NWorkflow {
  name: string;
  nodes: N8NNode[];
  connections: Record<string, Record<string, N8NConnection[][]>>;
  active: boolean;
  settings: Record<string, any>;
  staticData?: Record<string, any>;
}

// 工作流规划相关类型
export interface WorkflowGoal {
  id: string;
  title: string;
  description: string;
  category: 'data-sync' | 'notification' | 'automation' | 'integration' | 'monitoring';
  complexity: 'simple' | 'medium' | 'complex';
}

export interface TriggerType {
  id: string;
  name: string;
  description: string;
  nodeType: string;
  icon: string;
  category: 'schedule' | 'webhook' | 'file' | 'email' | 'database';
}

export interface ServiceIntegration {
  id: string;
  name: string;
  description: string;
  category: string;
  nodeTypes: string[];
  authRequired: boolean;
  documentationUrl: string;
  icon: string;
}

export interface WorkflowPlan {
  goal: WorkflowGoal;
  trigger: TriggerType;
  services: ServiceIntegration[];
  steps: PlanStep[];
  estimatedComplexity: 'simple' | 'medium' | 'complex';
  recommendedNodes: string[];
}

export interface PlanStep {
  id: string;
  title: string;
  description: string;
  nodeType: string;
  required: boolean;
  order: number;
}

// 节点配置相关类型
export interface NodeTemplate {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  parameters: NodeParameter[];
  credentials?: string[];
  documentation: string;
}

export interface NodeParameter {
  name: string;
  displayName: string;
  type: 'string' | 'number' | 'boolean' | 'options' | 'json' | 'collection';
  required: boolean;
  default?: any;
  description: string;
  options?: Array<{ name: string; value: any; description?: string }>;
  placeholder?: string;
}

// 用户配置状态
export interface UserConfiguration {
  currentStep: number;
  workflowPlan?: WorkflowPlan;
  nodeConfigurations: Record<string, Record<string, any>>;
  connections: Array<{ from: string; to: string }>;
}

// 知识库相关类型
export interface KnowledgeItem {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  url: string;
  type: 'documentation' | 'tutorial' | 'example' | 'api-reference';
}

export interface CodeSnippet {
  id: string;
  title: string;
  description: string;
  language: string;
  code: string;
  nodeType: string;
  category: string;
}
