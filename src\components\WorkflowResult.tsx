'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Alert, Badge, Modal, Form } from 'react-bootstrap';
import { Download, Copy, Eye, FileText, ExternalLink, CheckCircle, ArrowLeft } from 'lucide-react';
import { N8NWorkflow } from '@/types/workflow';

interface WorkflowResultProps {
  workflow: N8NWorkflow;
  onBack: () => void;
  onStartOver: () => void;
}

const WorkflowResult: React.FC<WorkflowResultProps> = ({ 
  workflow, 
  onBack, 
  onStartOver 
}) => {
  const [showJsonModal, setShowJsonModal] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [copied, setCopied] = useState(false);

  const workflowJson = JSON.stringify(workflow, null, 2);

  const handleDownload = () => {
    const blob = new Blob([workflowJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(workflowJson);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const getNodeStats = () => {
    const nodeTypes = workflow.nodes.reduce((acc, node) => {
      const category = node.type.includes('trigger') ? '触发器' :
                     node.type.includes('webhook') ? '触发器' :
                     node.type.includes('cron') ? '触发器' :
                     node.type.includes('http') ? 'API' :
                     node.type.includes('wecom') ? '通讯' :
                     node.type.includes('email') ? '通讯' :
                     node.type.includes('code') ? '数据处理' :
                     node.type.includes('json') ? '数据处理' :
                     node.type.includes('if') ? '逻辑' : '其他';
      
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return nodeTypes;
  };

  const renderImportInstructions = () => (
    <Modal show={showInstructions} onHide={() => setShowInstructions(false)} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>📥 导入N8N工作流指南</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Alert variant="info">
          <Alert.Heading className="h6">🎯 导入步骤</Alert.Heading>
          <ol className="mb-0">
            <li>打开您的N8N实例</li>
            <li>点击右上角的 "+" 按钮创建新工作流</li>
            <li>点击左上角的菜单按钮（三条横线）</li>
            <li>选择 "Import from file" 或 "从文件导入"</li>
            <li>选择刚才下载的JSON文件</li>
            <li>工作流将自动加载到编辑器中</li>
          </ol>
        </Alert>

        <Alert variant="warning">
          <Alert.Heading className="h6">⚠️ 导入后需要配置</Alert.Heading>
          <ul className="mb-0">
            <li><strong>凭据设置：</strong> 为需要认证的节点配置相应的凭据</li>
            <li><strong>参数检查：</strong> 确认所有节点的参数设置正确</li>
            <li><strong>测试运行：</strong> 建议先在测试环境中运行工作流</li>
            <li><strong>激活工作流：</strong> 确认无误后激活工作流</li>
          </ul>
        </Alert>

        <Alert variant="success">
          <Alert.Heading className="h6">✅ 有用的资源</Alert.Heading>
          <ul className="mb-0">
            <li>
              <a href="https://docs.n8n.io/" target="_blank" rel="noopener noreferrer">
                N8N官方文档 <ExternalLink size={14} className="ms-1" />
              </a>
            </li>
            <li>
              <a href="https://community.n8n.io/" target="_blank" rel="noopener noreferrer">
                N8N社区论坛 <ExternalLink size={14} className="ms-1" />
              </a>
            </li>
            <li>
              <a href="https://docs.n8n.io/workflows/workflows/" target="_blank" rel="noopener noreferrer">
                工作流管理指南 <ExternalLink size={14} className="ms-1" />
              </a>
            </li>
          </ul>
        </Alert>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={() => setShowInstructions(false)}>
          我知道了
        </Button>
      </Modal.Footer>
    </Modal>
  );

  const renderJsonModal = () => (
    <Modal show={showJsonModal} onHide={() => setShowJsonModal(false)} size="xl">
      <Modal.Header closeButton>
        <Modal.Title>📄 工作流JSON代码</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="position-relative">
          <pre className="code-block" style={{ maxHeight: '500px', overflow: 'auto' }}>
            <code>{workflowJson}</code>
          </pre>
          <Button
            variant="outline-secondary"
            size="sm"
            className="position-absolute top-0 end-0 m-2"
            onClick={handleCopy}
          >
            {copied ? <CheckCircle size={14} /> : <Copy size={14} />}
            {copied ? ' 已复制' : ' 复制'}
          </Button>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={() => setShowJsonModal(false)}>
          关闭
        </Button>
        <Button variant="primary" onClick={handleDownload}>
          <Download size={16} className="me-1" />
          下载文件
        </Button>
      </Modal.Footer>
    </Modal>
  );

  const nodeStats = getNodeStats();

  return (
    <Container className="py-4">
      <div className="text-center mb-5">
        <div className="mb-3">
          <CheckCircle size={64} className="text-success" />
        </div>
        <h2 className="text-success mb-2">🎉 工作流生成完成！</h2>
        <p className="text-muted">您的N8N工作流已经准备就绪，可以下载并导入使用</p>
      </div>

      <Row>
        <Col lg={8}>
          {/* 工作流信息 */}
          <Card className="mb-4 shadow-sm">
            <Card.Header className="bg-success text-white">
              <h5 className="mb-0">📋 工作流信息</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <strong>工作流名称：</strong>
                    <div className="mt-1">{workflow.name}</div>
                  </div>
                  <div className="mb-3">
                    <strong>节点数量：</strong>
                    <Badge bg="primary" className="ms-2">{workflow.nodes.length}</Badge>
                  </div>
                  <div className="mb-3">
                    <strong>状态：</strong>
                    <Badge bg={workflow.active ? 'success' : 'secondary'} className="ms-2">
                      {workflow.active ? '激活' : '未激活'}
                    </Badge>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <strong>节点类型分布：</strong>
                    <div className="mt-2">
                      {Object.entries(nodeStats).map(([category, count]) => (
                        <Badge key={category} bg="outline-secondary" className="me-2 mb-1">
                          {category}: {count}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* 节点列表 */}
          <Card className="mb-4 shadow-sm">
            <Card.Header className="bg-light">
              <h5 className="mb-0">🧩 包含的节点</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                {workflow.nodes.map((node, index) => (
                  <Col md={6} key={node.id} className="mb-3">
                    <Card className="h-100 border-0 bg-light">
                      <Card.Body className="py-2">
                        <div className="d-flex align-items-center">
                          <div className="step-indicator completed me-2">
                            {index + 1}
                          </div>
                          <div>
                            <h6 className="mb-0">{node.name}</h6>
                            <small className="text-muted">{node.type}</small>
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          {/* 操作面板 */}
          <Card className="shadow-sm sticky-top">
            <Card.Header className="bg-light">
              <h5 className="mb-0">🚀 下一步操作</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-3">
                <Button
                  variant="success"
                  size="lg"
                  onClick={handleDownload}
                >
                  <Download size={20} className="me-2" />
                  下载工作流文件
                </Button>

                <Button
                  variant="outline-info"
                  onClick={() => setShowInstructions(true)}
                >
                  <FileText size={16} className="me-1" />
                  查看导入指南
                </Button>

                <Button
                  variant="outline-secondary"
                  onClick={() => setShowJsonModal(true)}
                >
                  <Eye size={16} className="me-1" />
                  查看JSON代码
                </Button>

                <hr />

                <Button
                  variant="outline-primary"
                  onClick={onBack}
                >
                  <ArrowLeft size={16} className="me-1" />
                  返回构建器
                </Button>

                <Button
                  variant="outline-secondary"
                  onClick={onStartOver}
                >
                  重新开始
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* 快速提示 */}
          <Alert variant="info" className="mt-3">
            <Alert.Heading className="h6">💡 快速提示</Alert.Heading>
            <ul className="mb-0 small">
              <li>下载的JSON文件可以直接导入N8N</li>
              <li>导入后请检查所有节点配置</li>
              <li>需要认证的节点请配置相应凭据</li>
              <li>建议先在测试环境中验证</li>
            </ul>
          </Alert>
        </Col>
      </Row>

      {renderJsonModal()}
      {renderImportInstructions()}
    </Container>
  );
};

export default WorkflowResult;
