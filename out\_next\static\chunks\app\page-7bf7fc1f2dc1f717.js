(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{613:function(e,n,o){Promise.resolve().then(o.bind(o,6117))},6117:function(e,n,o){"use strict";o.r(n),o.d(n,{default:function(){return d}});var i=o(7437),r=o(2265);function d(){var e,n;let[o,d]=(0,r.useState)(1),[t,l]=(0,r.useState)(""),[s,c]=(0,r.useState)(""),a=[{id:"notification",name:"消息通知",desc:"自动发送通知消息到企业微信、飞书等"},{id:"data-sync",name:"数据同步",desc:"在不同系统之间同步数据"},{id:"file-process",name:"文件处理",desc:"自动处理文件上传、转换等操作"}],x=[{id:"webhook",name:"Webhook触发",desc:"HTTP请求触发工作流"},{id:"schedule",name:"定时触发",desc:"按时间计划自动执行"},{id:"manual",name:"手动触发",desc:"手动执行工作流"}],p={padding:"20px",margin:"10px 0",border:"2px solid #e2e8f0",borderRadius:"12px",cursor:"pointer",transition:"all 0.3s ease",backgroundColor:"white"},h={...p,borderColor:"#6366f1",backgroundColor:"#f0f0ff",transform:"translateY(-2px)",boxShadow:"0 4px 12px rgba(99, 102, 241, 0.15)"};return(0,i.jsxs)("div",{style:{minHeight:"100vh",padding:"20px",maxWidth:"800px",margin:"0 auto",backgroundColor:"#f8fafc"},children:[(0,i.jsxs)("div",{style:{textAlign:"center",marginBottom:"40px"},children:[(0,i.jsx)("h1",{style:{color:"#6366f1",fontSize:"2.5rem",marginBottom:"10px",fontWeight:"bold"},children:"\uD83D\uDE80 N8N工作流构建器"}),(0,i.jsx)("p",{style:{color:"#64748b",fontSize:"1.1rem"},children:"通过简单的步骤创建专业的自动化工作流"})]}),(0,i.jsx)("div",{style:{display:"flex",justifyContent:"center",marginBottom:"40px",alignItems:"center"},children:[1,2,3].map(e=>(0,i.jsxs)(r.Fragment,{children:[(0,i.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",backgroundColor:o>=e?"#6366f1":"#e2e8f0",color:o>=e?"white":"#64748b",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"1.1rem"},children:o>e?"✓":e}),e<3&&(0,i.jsx)("div",{style:{width:"60px",height:"4px",backgroundColor:o>e?"#6366f1":"#e2e8f0",margin:"0 10px"}})]},e))}),1===o&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:{textAlign:"center",color:"#1e293b",marginBottom:"30px"},children:"\uD83C\uDFAF 第1步：选择您的自动化目标"}),(0,i.jsx)("div",{children:a.map(e=>(0,i.jsxs)("div",{onClick:()=>l(e.id),style:t===e.id?h:p,children:[(0,i.jsx)("h3",{style:{margin:"0 0 10px 0",color:"#1e293b"},children:e.name}),(0,i.jsx)("p",{style:{margin:0,color:"#64748b"},children:e.desc})]},e.id))}),(0,i.jsx)("div",{style:{textAlign:"center",marginTop:"30px"},children:(0,i.jsx)("button",{onClick:()=>d(2),disabled:!t,style:{padding:"12px 30px",backgroundColor:t?"#6366f1":"#94a3b8",color:"white",border:"none",borderRadius:"8px",cursor:t?"pointer":"not-allowed",fontSize:"1rem",fontWeight:"bold"},children:"下一步 →"})})]}),2===o&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:{textAlign:"center",color:"#1e293b",marginBottom:"30px"},children:"⚡ 第2步：选择触发方式"}),(0,i.jsx)("div",{children:x.map(e=>(0,i.jsxs)("div",{onClick:()=>c(e.id),style:s===e.id?h:p,children:[(0,i.jsx)("h3",{style:{margin:"0 0 10px 0",color:"#1e293b"},children:e.name}),(0,i.jsx)("p",{style:{margin:0,color:"#64748b"},children:e.desc})]},e.id))}),(0,i.jsxs)("div",{style:{textAlign:"center",marginTop:"30px"},children:[(0,i.jsx)("button",{onClick:()=>d(1),style:{padding:"12px 30px",backgroundColor:"#6b7280",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"1rem",marginRight:"15px"},children:"← 上一步"}),(0,i.jsx)("button",{onClick:()=>d(3),disabled:!s,style:{padding:"12px 30px",backgroundColor:s?"#6366f1":"#94a3b8",color:"white",border:"none",borderRadius:"8px",cursor:s?"pointer":"not-allowed",fontSize:"1rem",fontWeight:"bold"},children:"生成工作流 →"})]})]}),3===o&&(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{style:{textAlign:"center",marginBottom:"30px"},children:[(0,i.jsx)("div",{style:{fontSize:"4rem",marginBottom:"20px"},children:"\uD83C\uDF89"}),(0,i.jsx)("h2",{style:{color:"#10b981",marginBottom:"10px"},children:"工作流生成完成！"}),(0,i.jsx)("p",{style:{color:"#64748b"},children:"您的N8N工作流已经准备就绪"})]}),(0,i.jsxs)("div",{style:{padding:"25px",backgroundColor:"white",borderRadius:"12px",border:"2px solid #10b981",marginBottom:"30px"},children:[(0,i.jsx)("h3",{style:{color:"#1e293b",marginBottom:"15px"},children:"\uD83D\uDCCB 您的配置："}),(0,i.jsxs)("div",{style:{marginBottom:"10px"},children:[(0,i.jsx)("strong",{children:"目标："})," ",null===(e=a.find(e=>e.id===t))||void 0===e?void 0:e.name]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"触发器："})," ",null===(n=x.find(e=>e.id===s))||void 0===n?void 0:n.name]})]}),(0,i.jsxs)("div",{style:{textAlign:"center",marginBottom:"30px"},children:[(0,i.jsx)("button",{onClick:()=>{var e,n,o,i;let r={name:"".concat(null===(e=a.find(e=>e.id===t))||void 0===e?void 0:e.name," - ").concat(null===(n=x.find(e=>e.id===s))||void 0===n?void 0:n.name),nodes:[{id:"trigger_node",name:(null===(o=x.find(e=>e.id===s))||void 0===o?void 0:o.name)||"触发器",type:"webhook"===s?"n8n-nodes-base.webhook":"schedule"===s?"n8n-nodes-base.cron":"n8n-nodes-base.manualTrigger",position:[100,100],parameters:"webhook"===s?{path:"my-webhook",httpMethod:"POST"}:"schedule"===s?{mode:"everyHour"}:{}},{id:"action_node",name:"执行动作",type:"notification"===t?"n8n-nodes-base.httpRequest":"n8n-nodes-base.code",position:[300,100],parameters:"notification"===t?{url:"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY",method:"POST"}:{jsCode:"// 处理数据的代码\nreturn items;"}}],connections:{[(null===(i=x.find(e=>e.id===s))||void 0===i?void 0:i.name)||"触发器"]:{main:[[{node:"执行动作",type:"main",index:0}]]}},active:!1},d=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),l=URL.createObjectURL(d),c=document.createElement("a");c.href=l,c.download="".concat(r.name.replace(/[^a-zA-Z0-9]/g,"_"),".json"),document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(l)},style:{padding:"15px 40px",backgroundColor:"#10b981",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"1.1rem",fontWeight:"bold",marginRight:"15px"},children:"\uD83D\uDCE5 下载工作流文件"}),(0,i.jsx)("button",{onClick:()=>{d(1),l(""),c("")},style:{padding:"15px 40px",backgroundColor:"#6b7280",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"1.1rem"},children:"\uD83D\uDD04 重新开始"})]}),(0,i.jsxs)("div",{style:{padding:"20px",backgroundColor:"#fffbeb",borderRadius:"8px",border:"1px solid #fbbf24"},children:[(0,i.jsx)("h4",{style:{color:"#92400e",marginBottom:"15px"},children:"\uD83D\uDCD6 如何使用生成的文件："}),(0,i.jsxs)("ol",{style:{color:"#92400e",paddingLeft:"20px"},children:[(0,i.jsx)("li",{children:"打开您的N8N实例"}),(0,i.jsx)("li",{children:'点击"+"创建新工作流'}),(0,i.jsx)("li",{children:'点击菜单 → "Import from file"'}),(0,i.jsx)("li",{children:"选择刚才下载的JSON文件"}),(0,i.jsx)("li",{children:"配置节点参数并激活工作流"})]})]})]})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=613)}),_N_E=e.O()}]);