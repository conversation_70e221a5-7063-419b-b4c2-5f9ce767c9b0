"""
AutoGen编程工作流配置文件
包含各种配置选项和环境设置
"""

import os
from typing import Dict, Any, List
from pathlib import Path


class WorkflowConfig:
    """工作流配置类"""
    
    # OpenAI配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    DEFAULT_MODEL = "gpt-4o"
    TEMPERATURE = 0.7
    MAX_TOKENS = 4000
    
    # 工作目录配置
    WORKSPACE_DIR = Path("coding_workspace")
    RESULTS_DIR = Path("results")
    LOGS_DIR = Path("logs")
    
    # Agent配置
    MAX_MESSAGES = 15
    TIMEOUT_SECONDS = 300
    
    # 支持的编程语言
    SUPPORTED_LANGUAGES = {
        "python": {
            "extension": ".py",
            "test_command": ["python", "-m", "py_compile"],
            "run_command": ["python"]
        },
        "javascript": {
            "extension": ".js", 
            "test_command": ["node", "--check"],
            "run_command": ["node"]
        },
        "java": {
            "extension": ".java",
            "test_command": ["javac"],
            "run_command": ["java"]
        },
        "cpp": {
            "extension": ".cpp",
            "test_command": ["g++", "-fsyntax-only"],
            "run_command": ["g++", "-o"]
        }
    }
    
    # Agent系统消息模板
    AGENT_TEMPLATES = {
        "coder": """你是一个专业的{language}代码编写专家。你的职责是：

1. 仔细分析用户需求，理解功能要求
2. 编写清晰、可读、高效的{language}代码
3. 遵循{language}的最佳编程实践和代码规范
4. 添加适当的注释和文档字符串
5. 考虑错误处理和边界情况
6. 使用合适的设计模式和架构

编写完代码后，请说明你的设计思路和关键实现点。
完成后请说 'CODE_READY' 以便进入审查阶段。""",
        
        "reviewer": """你是一个经验丰富的{language}代码审查专家。你的职责是：

1. 仔细审查代码的正确性、可读性和性能
2. 检查是否遵循{language}编程最佳实践
3. 识别潜在的bug、安全问题和性能瓶颈
4. 评估代码的可维护性和可扩展性
5. 提出具体的改进建议和优化方案
6. 检查错误处理和边界情况的处理

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 安全性考虑

完成审查后请说 'REVIEW_COMPLETE' 以便进入优化阶段。""",
        
        "optimizer": """你是一个{language}代码优化专家。你的职责是：

1. 分析原始代码和审查建议
2. 实施代码优化和重构
3. 提升代码性能、可读性和可维护性
4. 修复发现的bug和安全问题
5. 优化算法和数据结构
6. 改进错误处理和异常管理
7. 添加或改进测试用例

请提供优化后的完整代码，并说明：
- 实施的优化措施
- 性能改进点
- 代码质量提升
- 修复的问题

完成优化后请说 'OPTIMIZATION_COMPLETE'。"""
    }
    
    @classmethod
    def setup_directories(cls):
        """创建必要的目录"""
        for directory in [cls.WORKSPACE_DIR, cls.RESULTS_DIR, cls.LOGS_DIR]:
            directory.mkdir(exist_ok=True)
    
    @classmethod
    def get_agent_message(cls, agent_type: str, language: str = "Python") -> str:
        """获取agent系统消息"""
        template = cls.AGENT_TEMPLATES.get(agent_type, "")
        return template.format(language=language)
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """验证配置"""
        issues = []
        
        if not cls.OPENAI_API_KEY:
            issues.append("OPENAI_API_KEY环境变量未设置")
        
        if cls.TEMPERATURE < 0 or cls.TEMPERATURE > 2:
            issues.append("TEMPERATURE应该在0-2之间")
        
        if cls.MAX_MESSAGES < 1:
            issues.append("MAX_MESSAGES应该大于0")
        
        return issues


# 预定义的编程任务模板
TASK_TEMPLATES = {
    "data_structure": """
请实现一个{data_structure_name}数据结构，要求：
1. 实现所有基本操作
2. 包含完整的错误处理
3. 添加详细的文档字符串
4. 提供使用示例
5. 分析时间和空间复杂度
""",
    
    "algorithm": """
请实现{algorithm_name}算法，要求：
1. 实现完整的算法逻辑
2. 优化时间和空间复杂度
3. 处理边界情况
4. 添加详细注释
5. 提供测试用例
6. 分析算法复杂度
""",
    
    "web_api": """
请设计并实现一个{api_name} Web API，要求：
1. 使用RESTful设计原则
2. 包含完整的CRUD操作
3. 实现输入验证和错误处理
4. 添加API文档
5. 包含单元测试
6. 考虑安全性和性能
""",
    
    "system_design": """
请设计并实现一个{system_name}系统，要求：
1. 清晰的系统架构设计
2. 模块化和可扩展的代码结构
3. 完整的错误处理和日志记录
4. 性能优化和资源管理
5. 详细的设计文档
6. 单元测试和集成测试
"""
}


def get_task_from_template(template_name: str, **kwargs) -> str:
    """从模板生成任务描述"""
    template = TASK_TEMPLATES.get(template_name, "")
    return template.format(**kwargs)


# 示例配置使用
if __name__ == "__main__":
    # 验证配置
    issues = WorkflowConfig.validate_config()
    if issues:
        print("配置问题:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("✅ 配置验证通过")
    
    # 创建目录
    WorkflowConfig.setup_directories()
    print("✅ 工作目录已创建")
    
    # 示例任务生成
    task = get_task_from_template(
        "data_structure",
        data_structure_name="红黑树"
    )
    print(f"\n示例任务:\n{task}")
