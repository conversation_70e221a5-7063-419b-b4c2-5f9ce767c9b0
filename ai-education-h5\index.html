<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Education H5 Prototype</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem 0;
        }
        .iphone-15-pro {
            width: 393px; /* iPhone 15 Pro width */
            background-color: #ffffff;
            border-radius: 50px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 15px;
            border: 8px solid #111;
            position: relative;
            overflow: hidden;
        }
        .screen-container {
            width: 100%;
            height: 852px; /* iPhone 15 Pro height */
            background-color: #fff;
            border-radius: 42px;
            overflow-y: auto;
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .screen-container::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        .status-bar {
            height: 44px;
            background-color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-size: 15px;
            font-weight: 600;
            color: #1f1f1f;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .status-bar .time {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .status-bar .right-icons {
            display: flex;
            gap: 8px;
        }
        .page-section {
            margin-bottom: 40px;
        }
        .page-title {
            font-size: 24px;
            font-weight: bold;
            margin: 20px;
            color: #333;
        }
        iframe {
            width: 100%;
            border: none;
            display: block;
        }
        .bottom-nav {
            position: sticky;
            bottom: 0;
            width: 100%;
            height: 83px;
            background-color: rgba(249, 249, 249, 0.94);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding-top: 10px;
            z-index: 10;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
            text-decoration: none;
        }
        .nav-item.active {
            color: #007aff;
        }
        .nav-item i {
            font-size: 22px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="iphone-15-pro">
        <div class="screen-container">
            <div class="status-bar">
                <span class="time">9:41</span>
                <div class="right-icons">
                    <i class="fa-solid fa-signal"></i>
                    <i class="fa-solid fa-wifi"></i>
                    <i class="fa-solid fa-battery-full"></i>
                </div>
            </div>

            <div id="content">
                <div class="page-section">
                    <h2 class="page-title">首页 (Home)</h2>
                    <iframe src="home.html" id="home-frame" onload="resizeIframe(this)"></iframe>
                </div>
                <div class="page-section">
                    <h2 class="page-title">课程列表 (Courses)</h2>
                    <iframe src="courses.html" id="courses-frame" onload="resizeIframe(this)"></iframe>
                </div>
                <div class="page-section">
                    <h2 class="page-title">课程详情 (Course Detail)</h2>
                    <iframe src="course_detail.html" id="course-detail-frame" onload="resizeIframe(this)"></iframe>
                </div>
                <div class="page-section">
                    <h2 class="page-title">学习界面 (Learning)</h2>
                    <iframe src="learning.html" id="learning-frame" onload="resizeIframe(this)"></iframe>
                </div>
                <div class="page-section">
                    <h2 class="page-title">个人中心 (Profile)</h2>
                    <iframe src="profile.html" id="profile-frame" onload="resizeIframe(this)"></iframe>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#home-frame" class="nav-item active">
                    <i class="fa-solid fa-house"></i>
                    <span>首页</span>
                </a>
                <a href="#courses-frame" class="nav-item">
                    <i class="fa-solid fa-book-open"></i>
                    <span>课程</span>
                </a>
                <a href="#learning-frame" class="nav-item">
                    <i class="fa-solid fa-play-circle"></i>
                    <span>学习</span>
                </a>
                <a href="#profile-frame" class="nav-item">
                    <i class="fa-solid fa-user"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 'px';
        }

        // Smooth scroll for nav links
        document.querySelectorAll('.nav-item').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetFrame = document.querySelector(targetId);
                if (targetFrame) {
                    targetFrame.scrollIntoView({
                        behavior: 'smooth'
                    });
                    
                    // Update active state
                    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
