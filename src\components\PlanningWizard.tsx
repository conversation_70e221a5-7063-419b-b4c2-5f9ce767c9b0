'use client';

import React, { useState } from 'react';
import { Container, Row, Col, Card, Button, Form, Badge, Alert } from 'react-bootstrap';
import { ChevronRight, ChevronLeft, Target, Zap, Settings, CheckCircle } from 'lucide-react';
import { WorkflowGoal, TriggerType, ServiceIntegration, WorkflowPlan } from '@/types/workflow';
import { workflowGoals, triggerTypes, serviceIntegrations } from '@/data/workflowGoals';

interface PlanningWizardProps {
  onPlanComplete: (plan: WorkflowPlan) => void;
}

const PlanningWizard: React.FC<PlanningWizardProps> = ({ onPlanComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedGoal, setSelectedGoal] = useState<WorkflowGoal | null>(null);
  const [selectedTrigger, setSelectedTrigger] = useState<TriggerType | null>(null);
  const [selectedServices, setSelectedServices] = useState<ServiceIntegration[]>([]);
  const [customDescription, setCustomDescription] = useState('');

  const steps = [
    { title: '选择目标', icon: Target, description: '您想要实现什么样的自动化？' },
    { title: '选择触发器', icon: Zap, description: '什么事件会启动这个流程？' },
    { title: '选择服务', icon: Settings, description: '需要集成哪些应用或服务？' },
    { title: '确认规划', icon: CheckCircle, description: '确认您的工作流规划' }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleServiceToggle = (service: ServiceIntegration) => {
    setSelectedServices(prev => {
      const exists = prev.find(s => s.id === service.id);
      if (exists) {
        return prev.filter(s => s.id !== service.id);
      } else {
        return [...prev, service];
      }
    });
  };

  const generatePlan = (): WorkflowPlan => {
    if (!selectedGoal || !selectedTrigger) {
      throw new Error('请完成所有必需的选择');
    }

    const steps = [
      {
        id: 'trigger',
        title: '触发器',
        description: selectedTrigger.description,
        nodeType: selectedTrigger.nodeType,
        required: true,
        order: 1
      },
      ...selectedServices.map((service, index) => ({
        id: service.id,
        title: service.name,
        description: service.description,
        nodeType: service.nodeTypes[0],
        required: true,
        order: index + 2
      }))
    ];

    return {
      goal: selectedGoal,
      trigger: selectedTrigger,
      services: selectedServices,
      steps,
      estimatedComplexity: selectedGoal.complexity,
      recommendedNodes: [selectedTrigger.nodeType, ...selectedServices.flatMap(s => s.nodeTypes)]
    };
  };

  const handleComplete = () => {
    try {
      const plan = generatePlan();
      onPlanComplete(plan);
    } catch (error) {
      console.error('生成规划失败:', error);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: return selectedGoal !== null;
      case 1: return selectedTrigger !== null;
      case 2: return selectedServices.length > 0;
      case 3: return true;
      default: return false;
    }
  };

  const renderStepIndicator = () => (
    <Row className="mb-4">
      <Col>
        <div className="d-flex justify-content-between align-items-center">
          {steps.map((step, index) => (
            <div key={index} className="d-flex flex-column align-items-center">
              <div className={`step-indicator ${
                index < currentStep ? 'completed' : 
                index === currentStep ? 'active' : 'pending'
              }`}>
                {index < currentStep ? '✓' : index + 1}
              </div>
              <small className="mt-2 text-center" style={{ maxWidth: '80px' }}>
                {step.title}
              </small>
            </div>
          ))}
        </div>
        <div className="progress mt-3" style={{ height: '4px' }}>
          <div 
            className="progress-bar bg-primary" 
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </Col>
    </Row>
  );

  const renderGoalSelection = () => (
    <div className="fade-in">
      <h4 className="mb-4 text-center">🎯 您想要实现什么样的自动化？</h4>
      <Row>
        {workflowGoals.map(goal => (
          <Col md={6} lg={4} key={goal.id} className="mb-3">
            <Card 
              className={`h-100 workflow-step ${selectedGoal?.id === goal.id ? 'active' : ''}`}
              onClick={() => setSelectedGoal(goal)}
              style={{ cursor: 'pointer' }}
            >
              <Card.Body>
                <Card.Title className="d-flex align-items-center">
                  {goal.title}
                  <Badge 
                    bg={goal.complexity === 'simple' ? 'success' : 
                        goal.complexity === 'medium' ? 'warning' : 'danger'}
                    className="ms-2"
                  >
                    {goal.complexity === 'simple' ? '简单' : 
                     goal.complexity === 'medium' ? '中等' : '复杂'}
                  </Badge>
                </Card.Title>
                <Card.Text>{goal.description}</Card.Text>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  const renderTriggerSelection = () => (
    <div className="fade-in">
      <h4 className="mb-4 text-center">⚡ 什么事件会启动这个流程？</h4>
      <Row>
        {triggerTypes.map(trigger => (
          <Col md={6} lg={4} key={trigger.id} className="mb-3">
            <Card 
              className={`h-100 workflow-step ${selectedTrigger?.id === trigger.id ? 'active' : ''}`}
              onClick={() => setSelectedTrigger(trigger)}
              style={{ cursor: 'pointer' }}
            >
              <Card.Body className="text-center">
                <div style={{ fontSize: '2rem' }} className="mb-2">{trigger.icon}</div>
                <Card.Title>{trigger.name}</Card.Title>
                <Card.Text>{trigger.description}</Card.Text>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  const renderServiceSelection = () => (
    <div className="fade-in">
      <h4 className="mb-4 text-center">🔧 需要集成哪些应用或服务？</h4>
      <Alert variant="info" className="mb-4">
        <strong>提示：</strong> 选择您需要在工作流中使用的服务。可以选择多个服务。
      </Alert>
      
      {/* 按类别分组显示服务 */}
      {Array.from(new Set(serviceIntegrations.map(s => s.category))).map(category => (
        <div key={category} className="mb-4">
          <h5 className="mb-3">{category}</h5>
          <Row>
            {serviceIntegrations
              .filter(service => service.category === category)
              .map(service => (
                <Col md={6} lg={4} key={service.id} className="mb-3">
                  <Card 
                    className={`h-100 node-card ${
                      selectedServices.find(s => s.id === service.id) ? 'selected' : ''
                    }`}
                    onClick={() => handleServiceToggle(service)}
                  >
                    <Card.Body>
                      <div className="d-flex align-items-center mb-2">
                        <span style={{ fontSize: '1.5rem' }} className="me-2">{service.icon}</span>
                        <Card.Title className="mb-0">{service.name}</Card.Title>
                        {service.authRequired && (
                          <Badge bg="warning" className="ms-2">需认证</Badge>
                        )}
                      </div>
                      <Card.Text>{service.description}</Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
          </Row>
        </div>
      ))}
    </div>
  );

  const renderPlanConfirmation = () => {
    if (!selectedGoal || !selectedTrigger) return null;

    return (
      <div className="fade-in">
        <h4 className="mb-4 text-center">📋 确认您的工作流规划</h4>
        
        <Card className="mb-4">
          <Card.Header>
            <h5 className="mb-0">🎯 自动化目标</h5>
          </Card.Header>
          <Card.Body>
            <h6>{selectedGoal.title}</h6>
            <p className="text-muted">{selectedGoal.description}</p>
          </Card.Body>
        </Card>

        <Card className="mb-4">
          <Card.Header>
            <h5 className="mb-0">⚡ 触发方式</h5>
          </Card.Header>
          <Card.Body>
            <div className="d-flex align-items-center">
              <span style={{ fontSize: '1.5rem' }} className="me-2">{selectedTrigger.icon}</span>
              <div>
                <h6 className="mb-1">{selectedTrigger.name}</h6>
                <p className="text-muted mb-0">{selectedTrigger.description}</p>
              </div>
            </div>
          </Card.Body>
        </Card>

        <Card className="mb-4">
          <Card.Header>
            <h5 className="mb-0">🔧 集成服务 ({selectedServices.length})</h5>
          </Card.Header>
          <Card.Body>
            {selectedServices.length === 0 ? (
              <p className="text-muted">未选择任何服务</p>
            ) : (
              <Row>
                {selectedServices.map(service => (
                  <Col md={6} key={service.id} className="mb-2">
                    <div className="d-flex align-items-center">
                      <span style={{ fontSize: '1.2rem' }} className="me-2">{service.icon}</span>
                      <span>{service.name}</span>
                    </div>
                  </Col>
                ))}
              </Row>
            )}
          </Card.Body>
        </Card>

        <Form.Group className="mb-4">
          <Form.Label>补充说明（可选）</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            placeholder="请描述任何特殊需求或补充信息..."
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
          />
        </Form.Group>
      </div>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0: return renderGoalSelection();
      case 1: return renderTriggerSelection();
      case 2: return renderServiceSelection();
      case 3: return renderPlanConfirmation();
      default: return null;
    }
  };

  return (
    <Container className="py-4">
      {renderStepIndicator()}
      
      <Card className="shadow-sm">
        <Card.Header className="bg-primary text-white">
          <div className="d-flex align-items-center">
            {React.createElement(steps[currentStep].icon, { size: 24, className: 'me-2' })}
            <div>
              <h5 className="mb-0">{steps[currentStep].title}</h5>
              <small>{steps[currentStep].description}</small>
            </div>
          </div>
        </Card.Header>
        <Card.Body style={{ minHeight: '500px' }}>
          {renderCurrentStep()}
        </Card.Body>
        <Card.Footer className="d-flex justify-content-between">
          <Button 
            variant="outline-secondary" 
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            <ChevronLeft size={16} className="me-1" />
            上一步
          </Button>
          
          {currentStep === steps.length - 1 ? (
            <Button 
              variant="success" 
              onClick={handleComplete}
              disabled={!canProceed()}
            >
              生成工作流蓝图
              <CheckCircle size={16} className="ms-1" />
            </Button>
          ) : (
            <Button 
              variant="primary" 
              onClick={handleNext}
              disabled={!canProceed()}
            >
              下一步
              <ChevronRight size={16} className="ms-1" />
            </Button>
          )}
        </Card.Footer>
      </Card>
    </Container>
  );
};

export default PlanningWizard;
