import { NodeTemplate } from '@/types/workflow';

export const nodeTemplates: Record<string, NodeTemplate> = {
  'n8n-nodes-base.webhook': {
    type: 'n8n-nodes-base.webhook',
    name: 'Webhook',
    description: '接收HTTP请求触发工作流',
    category: '触发器',
    icon: '🔗',
    parameters: [
      {
        name: 'path',
        displayName: 'Webhook路径',
        type: 'string',
        required: true,
        default: '',
        description: 'Webhook的URL路径',
        placeholder: 'my-webhook'
      },
      {
        name: 'httpMethod',
        displayName: 'HTTP方法',
        type: 'options',
        required: true,
        default: 'POST',
        description: '接受的HTTP请求方法',
        options: [
          { name: 'GET', value: 'GET' },
          { name: 'POST', value: 'POST' },
          { name: 'PUT', value: 'PUT' },
          { name: 'DELETE', value: 'DELETE' }
        ]
      },
      {
        name: 'responseMode',
        displayName: '响应模式',
        type: 'options',
        required: true,
        default: 'onReceived',
        description: '何时返回响应',
        options: [
          { name: '立即响应', value: 'onReceived' },
          { name: '工作流完成后响应', value: 'lastNode' }
        ]
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook/'
  },

  'n8n-nodes-base.cron': {
    type: 'n8n-nodes-base.cron',
    name: 'Cron',
    description: '按时间计划触发工作流',
    category: '触发器',
    icon: '⏰',
    parameters: [
      {
        name: 'triggerTimes',
        displayName: '触发时间',
        type: 'collection',
        required: true,
        default: {},
        description: '设置触发的时间规则'
      },
      {
        name: 'mode',
        displayName: '模式',
        type: 'options',
        required: true,
        default: 'everyMinute',
        description: '触发频率',
        options: [
          { name: '每分钟', value: 'everyMinute' },
          { name: '每小时', value: 'everyHour' },
          { name: '每天', value: 'everyDay' },
          { name: '每周', value: 'everyWeek' },
          { name: '自定义', value: 'custom' }
        ]
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.cron/'
  },

  'n8n-nodes-base.httpRequest': {
    type: 'n8n-nodes-base.httpRequest',
    name: 'HTTP Request',
    description: '发送HTTP请求',
    category: 'API',
    icon: '🌐',
    parameters: [
      {
        name: 'url',
        displayName: 'URL',
        type: 'string',
        required: true,
        default: '',
        description: '请求的URL地址',
        placeholder: 'https://api.example.com/data'
      },
      {
        name: 'method',
        displayName: 'HTTP方法',
        type: 'options',
        required: true,
        default: 'GET',
        description: 'HTTP请求方法',
        options: [
          { name: 'GET', value: 'GET' },
          { name: 'POST', value: 'POST' },
          { name: 'PUT', value: 'PUT' },
          { name: 'DELETE', value: 'DELETE' },
          { name: 'PATCH', value: 'PATCH' }
        ]
      },
      {
        name: 'sendHeaders',
        displayName: '发送请求头',
        type: 'boolean',
        required: false,
        default: false,
        description: '是否发送自定义请求头'
      },
      {
        name: 'sendBody',
        displayName: '发送请求体',
        type: 'boolean',
        required: false,
        default: false,
        description: '是否发送请求体数据'
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/'
  },

  'n8n-nodes-base.wecom': {
    type: 'n8n-nodes-base.wecom',
    name: '企业微信',
    description: '发送企业微信消息',
    category: '通讯',
    icon: '💬',
    parameters: [
      {
        name: 'resource',
        displayName: '资源',
        type: 'options',
        required: true,
        default: 'message',
        description: '选择操作的资源类型',
        options: [
          { name: '消息', value: 'message' }
        ]
      },
      {
        name: 'operation',
        displayName: '操作',
        type: 'options',
        required: true,
        default: 'send',
        description: '选择要执行的操作',
        options: [
          { name: '发送', value: 'send' }
        ]
      },
      {
        name: 'webhookUrl',
        displayName: 'Webhook URL',
        type: 'string',
        required: true,
        default: '',
        description: '企业微信机器人的Webhook地址',
        placeholder: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=...'
      },
      {
        name: 'msgtype',
        displayName: '消息类型',
        type: 'options',
        required: true,
        default: 'text',
        description: '消息的类型',
        options: [
          { name: '文本', value: 'text' },
          { name: 'Markdown', value: 'markdown' }
        ]
      },
      {
        name: 'content',
        displayName: '消息内容',
        type: 'string',
        required: true,
        default: '',
        description: '要发送的消息内容',
        placeholder: '请输入消息内容'
      }
    ],
    documentation: 'https://developer.work.weixin.qq.com/document/path/91770'
  },

  'n8n-nodes-base.code': {
    type: 'n8n-nodes-base.code',
    name: 'Code',
    description: '执行JavaScript代码',
    category: '数据处理',
    icon: '💻',
    parameters: [
      {
        name: 'mode',
        displayName: '模式',
        type: 'options',
        required: true,
        default: 'runOnceForAllItems',
        description: '代码执行模式',
        options: [
          { name: '为所有项目运行一次', value: 'runOnceForAllItems' },
          { name: '为每个项目运行', value: 'runOnceForEachItem' }
        ]
      },
      {
        name: 'jsCode',
        displayName: 'JavaScript代码',
        type: 'string',
        required: true,
        default: '// 在这里编写您的代码\nreturn items;',
        description: '要执行的JavaScript代码',
        placeholder: '// 处理数据的代码'
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/'
  },

  'n8n-nodes-base.if': {
    type: 'n8n-nodes-base.if',
    name: 'IF',
    description: '条件判断节点',
    category: '逻辑',
    icon: '🔀',
    parameters: [
      {
        name: 'conditions',
        displayName: '条件',
        type: 'collection',
        required: true,
        default: {},
        description: '设置判断条件'
      },
      {
        name: 'combineOperation',
        displayName: '组合操作',
        type: 'options',
        required: true,
        default: 'all',
        description: '多个条件的组合方式',
        options: [
          { name: '所有条件都满足 (AND)', value: 'all' },
          { name: '任一条件满足 (OR)', value: 'any' }
        ]
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.if/'
  },

  'n8n-nodes-base.json': {
    type: 'n8n-nodes-base.json',
    name: 'JSON',
    description: 'JSON数据处理',
    category: '数据处理',
    icon: '📋',
    parameters: [
      {
        name: 'operation',
        displayName: '操作',
        type: 'options',
        required: true,
        default: 'getProperty',
        description: '选择JSON操作类型',
        options: [
          { name: '获取属性', value: 'getProperty' },
          { name: '设置属性', value: 'setProperty' },
          { name: '删除属性', value: 'deleteProperty' }
        ]
      },
      {
        name: 'propertyName',
        displayName: '属性名',
        type: 'string',
        required: true,
        default: '',
        description: '要操作的JSON属性名',
        placeholder: 'data.name'
      }
    ],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.json/'
  },

  'n8n-nodes-base.emailSend': {
    type: 'n8n-nodes-base.emailSend',
    name: 'Send Email',
    description: '发送邮件',
    category: '通讯',
    icon: '📧',
    parameters: [
      {
        name: 'fromEmail',
        displayName: '发件人邮箱',
        type: 'string',
        required: true,
        default: '',
        description: '发件人的邮箱地址',
        placeholder: '<EMAIL>'
      },
      {
        name: 'toEmail',
        displayName: '收件人邮箱',
        type: 'string',
        required: true,
        default: '',
        description: '收件人的邮箱地址',
        placeholder: '<EMAIL>'
      },
      {
        name: 'subject',
        displayName: '邮件主题',
        type: 'string',
        required: true,
        default: '',
        description: '邮件的主题',
        placeholder: '邮件主题'
      },
      {
        name: 'text',
        displayName: '邮件内容',
        type: 'string',
        required: true,
        default: '',
        description: '邮件的文本内容',
        placeholder: '邮件正文内容'
      }
    ],
    credentials: ['smtp'],
    documentation: 'https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.emailsend/'
  }
};
