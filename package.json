{"name": "n8n-workflow-builder", "version": "1.0.0", "description": "智能N8N工作流构建器 - 通过引导式界面创建自动化流程", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "bootstrap": "^5.3.0", "react-bootstrap": "^2.9.0", "lucide-react": "^0.294.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "js-yaml": "^4.1.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.0", "@types/lodash": "^4.14.0", "@types/js-yaml": "^4.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "keywords": ["n8n", "workflow", "automation", "no-code", "builder", "wizard"], "author": "AI Assistant", "license": "MIT"}